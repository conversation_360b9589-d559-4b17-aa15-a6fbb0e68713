<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
	</array>
	<key>CFBundleDisplayName</key>
	<string>YPD</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photos for file management.</string>
	<key>NSDocumentsUsageDescription</key>
	<string>We need access to your documents for file storage.</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We use your location to make sure our services follow state laws on medical cannabis.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to make sure our services follow state laws on medical cannabis.</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
    <array>
        <string>com.yourperfectdoseinc.ypd.refresh</string>
        <string>com.yourperfectdoseinc.ypd.processing</string>
    </array>
	<key>NSDocumentsUsageDescription</key>
	<string>We need access to your documents for file storage.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We use your location to check YPD's availability in your region.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is required to check YPD's availability in your region.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photos for file management.</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>FontAwesome6_Brands.ttf</string>
		<string>FontAwesome6_Regular.ttf</string>
		<string>FontAwesome6_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Black.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>processing</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
