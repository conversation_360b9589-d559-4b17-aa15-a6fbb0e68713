@use '../variables' as *;

.safeAreaView {
  flex: 1;
}

.screen {
  flex: 1;
  padding-top: 20px;
}

.headerContainer {
  padding: 0 0 5px 28px;
}

.myProfile {
  padding: 0 0 0 8px;
}

.buttonContainer {
  flex: 1;
  padding: 20px;
}

.divider {
  height: 1px;
  background-color: $ypd-dark-grey;
  margin: 15px 0px 15px 0px;
  width: 97%;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.shareContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: $ypd-teal;
  padding: 10px 15px 15px 25px;
  margin: 10px 0;
}

.leftContent {
  flex: 1;
}

.rightContent {
  margin: 0 0 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shareLink {
  margin: 6px 0 0 0;
}
