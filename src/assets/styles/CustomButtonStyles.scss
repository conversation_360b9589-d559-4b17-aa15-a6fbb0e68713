@use '../variables' as *;

.continueButton {
  height: 50px;
  display: flex;
  justify-content: center;
  width: 123px;
}

.continueButtonText {
  font-weight: 500;
  text-align: center;
}

.primaryButton {
  @extend .continueButton;
  background-color: $ypd-old-green;
  border: none;
}

.newGreenButton {
  @extend .continueButton;
  background-color: $ypd-green;
  border: none;
}

.secondaryButton {
  @extend .continueButton;
  background-color: $ypd-mid-blue;
}

.tealButton {
  @extend .continueButton;
  background-color: $ypd-teal;
  border: 1px solid $ypd-teal;
}

.tealButtonDark {
  @extend .continueButton;
  background-color: $ypd-teal;
  border: 1px solid $ypd-green;
}

.purpleButton {
  @extend .continueButton;
  background-color: $ypd-purple;
}

.greyButton {
  @extend .continueButton;
  background-color: $ypd-light-grey;
}

.transparent {
  @extend .continueButton;
}

.outlineButton {
  @extend .continueButton;
  border: 1px solid $ypd-teal;
}

.outlineButtonDark {
  @extend .continueButton;
  border: 1px solid $ypd-white;
}

.lightOutlineButton {
  @extend .continueButton;
  border: 1px solid $ypd-dark-grey;
}

.greenOutlineButton {
  @extend .continueButton;
  border: 1px solid $ypd-green;
}

.greenOutlineButtonDark {
  @extend .continueButton;
  background-color: $ypd-text-grey
}

.highlightBlueButton {
  @extend .continueButton;
  background-color: $ypd-highlight-blue;
}

.disabledButton {
  @extend .continueButton;
  background-color: transparent;
  border: 1px solid $ypd-old-green;
}

.deleteButton {
  @extend .continueButton;
  background-color: $ypd-red;
}

.letUsKnowButton {
  @extend .continueButton;
  background-color: $ypd-blue50;
}

.buttonWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doseCircleButton {
  width: 43px;
  height: 43px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkButton {
  @extend .doseCircleButton;
  border: 2px solid $ypd-green;
  background-color: $ypd-light-green;
}

.missedButton {
  @extend .doseCircleButton;
  border: 2px solid $ypd-dark-grey;
  background-color: $ypd-light-grey;
}

.iconButtonStyle {
  width: 20px;
  height: 21px;
}

.divider {
  width: 1px;
  height: 24px;
}
