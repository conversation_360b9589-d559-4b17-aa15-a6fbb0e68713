@use '../variables' as *;

.container {
  padding: 50px 0;
}

.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding: 5px;
}

.expandedBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 10px 15px 15px 0;
}

.expandedText {
  flex: 1;
  margin: 0 10px 0 0;
  padding: 0 0 0 10px;
}

.divider {
  height: 1px;
  background-color: $stroke-divider-color;
  width: 100%;
  align-self: center;
}
