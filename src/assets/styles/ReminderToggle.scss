@use '../variables' as *;

.container {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 10px 16px;
  margin-top: 10px;
  opacity: 0.95;
}

.label {
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.toggle {
  width: 60px;
  height: 18px;
  border-radius: 10px;
  justify-content: center;
  position: relative;
}

.toggleText {
  font-size: 12px;
  font-weight: 600;
  position: absolute;
}

.knob {
  position: absolute;
  top: 50%;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
