@use '../variables' as *;

.safeArea {
    flex: 1;
    padding: 15px 15px 0 15px;
}

.scrollContainer {
    flex-grow: 1;
}

.mainHeading {
    padding-left: 10px;
}

.closeButton {
    align-items: flex-end;
}

.currentDoseSection {
    padding: 25px;
    margin: 10px -15px 0 -15px;
    flex-direction: row;
    background-color: #F7FAFA;
    align-items: center;
    justify-content: space-between;
}

.doseSection {
    padding: 10px 25px 10px 15px;
    margin: 7px -15px 0 -15px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.textContainer {
    flex: 1;
    gap: 2px;
}

.pastDosesSection {
    margin-top: 20px;
    padding-left: 10px;
}

.textArea {
    border-color: $ypd-dark-grey;
    margin: 0 0 10px 0;
    font-size: 14px;
    padding: 10px 0 0 20px;
}

.imageContainer {
    margin: 10px 10px 0 0;
    border-radius: 8px;
}

.buttonSection {
    margin: 20px 0 0 0;
}

.backButton {
    margin: 30px 0 0 0;
}
