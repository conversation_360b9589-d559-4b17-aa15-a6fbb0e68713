@use '../variables' as *;

.mainWrapper {
    flex: 1;
    justify-content: center;
    align-items: center;
}

.headingContent {
    padding: 10px 0 0 13px ;
}

.content {
    flex-direction: column;
    padding: 20px 50px 0 0;
    gap: 30px;
}

.whereToBuyContainer {
    flex-direction: row;
    justify-content: center;
    align-self: center;
    margin: 20px 0 20px 0;
    width: 91%;

}

.whereToBuyText {
    flex: 1;
    justify-content: center;
    padding: 0 0 0 10px;
}

.letUsKnowButton {
    justify-content: center;
    align-items: center;
}
