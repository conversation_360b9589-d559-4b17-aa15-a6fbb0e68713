@use '../variables' as *;

.container {
    flex: 1;
    justify-content: center;
    align-items: center;
}

.contentContainerStyle {
    padding: 0 0 20px 0;
}

.floatingButton {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 55px;
    height: 55px;
    border-radius: 32px;
    justify-content: center;
    align-items: center;
}

.errorMessage {
    text-align: center;
    margin: 20px 0 20px 0;
}

.successMessage {
    color: $ypd-green;
    text-align: center;
    margin: 20px 0 20px 0;
}

.modalOverlay {
    flex: 1;
    justify-content: flex-end;
    background-color: rgba(0, 0, 0, 0.3);
}

.bottomSheet {
    background-color: $ypd-white;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding:0px 20px 20px 20px;
}

.dragHandle {
    width: 50px;
    height: 5px;
    background-color: $ypd-mid-grey;
    border-radius: 3px;
    align-self: center;
    margin: 0 0 10px 0;
}

.crossIcon {
    align-self: flex-end;
    margin: 10px 0 10px 0;
}

.infoContainer {
    background-color: $ypd-light-grey;
    padding: 15px 15px 0 0;
    border-radius: 10px;
    margin:0 0 20px 0;
}

.infoRow {
    flex-direction: row;
    justify-content: space-between;
    margin:0 0 5px 0;
    padding: 5px 0 0 0 ;
}

.emailInputFocused {
    border-color: $ypd-green;
    background-color: $ypd-white;
    color: $ypd-black;
}

.invalidEmail {
    border-color: $ypd-red;
    background-color: $ypd-white;
    color: $ypd-black;
}
.textArea {
    border-color: $ypd-dark-grey;
    margin:0 0 10px 0;
    font-size: 14px;
    padding: 10px 0 0 20px;
}

.emailInput {
    flex: 1;
    border-bottom-width: 2px;
    border-color: $ypd-dark-grey;
    font-size: 14px;
    color: $ypd-black;
    padding: 10px 8px 10px 8px;
    margin:0 0 0 10px;
    border-radius: 5px;
}

.emailContainer {
    flex-direction: row;
    align-items: center;
    margin:0 0 15px 0;
    padding: 0 5px 0 5px ;
}

.buttonRow {
    flex-direction: row;
    justify-content: space-between;
    margin:20px 0 0 0;
}
