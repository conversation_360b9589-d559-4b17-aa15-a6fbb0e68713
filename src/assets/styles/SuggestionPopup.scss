@use '../variables' as *;

.modalBackground {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  flex: 1;
}

.modalContainer {
  border-radius: 10px;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.bulletPoints {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin: 0 0 10px 0;
}

.bulletPoint {
  width: 10px;
}

.bulletPointText {
  flex: 1;
  font-style: italic;
}

.buttonContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  width: 100%;
}

.button {
  border: 1px solid $ypd-old-green;
  margin: 5px;
  width: 30vw;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
}

.doseCheckin {
  margin: 0 0 15px 0;
}

.titleText {
  text-align: center;
  font-size: 22px;
}

.doseTypeText {
  font-weight: bold;
}

.divider {
  margin: 15px 0;
}

.judgmentText {
  text-align: center;
  font-size: 16px;
  margin: 0 0 10px 0;
}

.infoText {
  text-align: center;
  margin: 0 0 20px 0;
}

.lightModeImage {
  margin: 0 0 35px 0;
}

.darkModeImage {
  margin: 0 0 35px 0;
   mix-blend-mode: overlay;
   opacity: 0.65;
}

.darkModeImage {
  margin: 0 0 35px 0;
}

.themeToggleContainer {
  margin: 20px 0;
  width: 100%;
  align-items: center;
}

.toggleButtonContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-self: center;
  border-radius: 40px;
  width: 125px;
  padding: 5px;
}

.toggleOption {
  width: 45px;
  height: 45px;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
}
