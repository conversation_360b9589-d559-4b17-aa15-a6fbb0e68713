@use '../variables' as *;

.safeArea {
  flex: 1;
}

.container {
  display: flex;
  flex: 1;
  padding: 10px 15px 15px 15px;
  border-radius: 20px;
  margin: 0 10px 10px 10px;
}

.header {
  margin: 0 0 50px 0;
}

.headerIcons {
  margin: 0 0 50px 0;
  gap: 10px;
}

.backButtonContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 20px 37px 20px;
}

.backButtonContainerAndroid {
  padding: 0 30px 35px 35px;
}

.buttonsContainer {
  margin-top: 50px;
  width: 100%;
}

.buttonsRow {
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin: 0 0 40px 0;
}
