@use '../variables' as *;

.button {
  background-color: #0BDDB4;
  border-radius: 25px;
  padding: 15px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
}

.buttonText {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.modalContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
  height: 100%;
  width: 100%;
}

.modalContent {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
}

.closeButton {
  align-self: flex-end;
}

.title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
}

.starsContainer {
  display: flex;
  flex-direction: row;
  margin-bottom: 20px;
}

.input {
  width: 100%;
  height: 100px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 8px 10px;
  margin-bottom: 20px;
  font-size: 16px;
}

.submitButton {
  width: 100%;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.submitButtonText {
  font-size: 16px;
  font-weight: bold;
}

.alertContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
  height: 100%;
  width: 100%;
}

.alertBox {
  width: 100%;
  max-width: 300px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
}

.alertTitle {
  font-size: 20px;
  font-weight: bold;
  color: #0BDDB4;
  margin-top: 10px;
  margin-bottom: 10px;
}

.alertMessage {
  font-size: 16px;
  color: #333;
  text-align: center;
}
