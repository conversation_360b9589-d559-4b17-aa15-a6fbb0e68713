@use '../variables' as *;

.safeArea {
  flex: 1;
}

.container {
  display: flex;
  flex: 1;
  padding: 10px 15px 15px 15px;
  border-radius: 20px;
  margin: 0 10px 10px 10px;
}

.header {
  margin: 0 0 50px 0;
}

.headerIcons {
  margin: 0 0 50px 0;
  gap: 10px;
}

.sliders {
  width: 100%;
}

.sliderContainer {
  margin: 0 0 40px 0;
}

.sliderWithCustomTrack {
  position: relative;
  height: 40px;
  display: flex;
  justify-content: center;
  overflow: visible;
  margin: 5px 0 0 10px;
}

.segmentedTrackContainer {
  display: flex;
  flex-direction: row;
  position: absolute;
  height: 8px;
  top: 16px;
  left: 0;
  z-index: 1;
}

.segmentBlock {
  height: 15px;
}

.slider {
  height: 40px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}

.image {
  height: 37px;
  width: 37px;
}

.customThumb {
  position: absolute;
  z-index: 3;
  background-color: $ypd-white;
  border-radius: 50%;
  justify-content: center;
}

.emojiLabel {
  text-align: center;
  font-size: 10px;
  color: $ypd-teal;
  margin: 2px 0 0 0;
}

.sliderText {
  font-size: 12px;
}

.visible {
  opacity: 1;
}

.hidden {
  opacity: 0;
}

.buttonContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 20px 0 20px;
}

.buttonContainerAndroid {
  padding: 0 30px 35px 35px;
}

.modalOverlay {
  display: flex;
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
}

.modalContent {
  width: 90%;
  border-radius: 15px;
  padding: 20px;
  position: relative;
}

.modalCloseButton {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.modalButtons {
  display: flex;
  align-items: center;
  margin: 20px 0 0 0;
}

.highlightedBorder {
  border-color: $ypd-olive-green;
}

.textArea {
  border-color: $ypd-dark-grey;
  margin: 10px 0;
  font-size: 14px;
  padding: 10px 0 0 10px;
}
