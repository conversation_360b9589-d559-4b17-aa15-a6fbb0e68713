import Svg, { Path } from 'react-native-svg';
const JournalIcon = ({ width, height, stroke }) => (
  <Svg
    width={width}
    height={height}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M15.375 0H2.625C2.12772 0 1.65081 0.19542 1.29917 0.54327C0.947544 0.891119 0.75 1.3629 0.75 1.85484V2.22581H0.375C0.275544 2.22581 0.180161 2.26489 0.109835 2.33446C0.0395088 2.40403 0 2.49839 0 2.59677C0 2.69516 0.0395088 2.78952 0.109835 2.85909C0.180161 2.92866 0.275544 2.96774 0.375 2.96774H0.75V5.19355H0.375C0.275544 5.19355 0.180161 5.23263 0.109835 5.3022C0.0395088 5.37177 0 5.46613 0 5.56452C0 5.6629 0.0395088 5.75726 0.109835 5.82683C0.180161 5.8964 0.275544 5.93548 0.375 5.93548H0.75V8.16129H0.375C0.275544 8.16129 0.180161 8.20037 0.109835 8.26994C0.0395088 8.33951 0 8.43387 0 8.53226C0 8.63064 0.0395088 8.725 0.109835 8.79457C0.180161 8.86414 0.275544 8.90323 0.375 8.90323H0.75V11.129H0.375C0.275544 11.129 0.180161 11.1681 0.109835 11.2377C0.0395088 11.3073 0 11.4016 0 11.5C0 11.5984 0.0395088 11.6927 0.109835 11.7623C0.180161 11.8319 0.275544 11.871 0.375 11.871H0.75V14.0968H0.375C0.275544 14.0968 0.180161 14.1359 0.109835 14.2054C0.0395088 14.275 0 14.3694 0 14.4677C0 14.5661 0.0395088 14.6605 0.109835 14.7301C0.180161 14.7996 0.275544 14.8387 0.375 14.8387H0.75V17.0645H0.375C0.275544 17.0645 0.180161 17.1036 0.109835 17.1732C0.0395088 17.2427 0 17.3371 0 17.4355C0 17.5339 0.0395088 17.6282 0.109835 17.6978C0.180161 17.7674 0.275544 17.8065 0.375 17.8065H0.75V20.0323H0.375C0.275544 20.0323 0.180161 20.0713 0.109835 20.1409C0.0395088 20.2105 0 20.3048 0 20.4032C0 20.5016 0.0395088 20.596 0.109835 20.6655C0.180161 20.7351 0.275544 20.7742 0.375 20.7742H0.75V21.1452C0.75 21.6371 0.947544 22.1089 1.29917 22.4567C1.65081 22.8046 2.12772 23 2.625 23H15.375C15.8723 23 16.3492 22.8046 16.7008 22.4567C17.0525 22.1089 17.25 21.6371 17.25 21.1452V1.85484C17.25 1.3629 17.0525 0.891119 16.7008 0.54327C16.3492 0.19542 15.8723 0 15.375 0ZM16.5 21.1452C16.5 21.4403 16.3815 21.7234 16.1705 21.9321C15.9595 22.1408 15.6734 22.2581 15.375 22.2581H2.625C2.32663 22.2581 2.04048 22.1408 1.8295 21.9321C1.61853 21.7234 1.5 21.4403 1.5 21.1452V20.7742H1.875C1.97446 20.7742 2.06984 20.7351 2.14016 20.6655C2.21049 20.596 2.25 20.5016 2.25 20.4032C2.25 20.3048 2.21049 20.2105 2.14016 20.1409C2.06984 20.0713 1.97446 20.0323 1.875 20.0323H1.5V17.8065H1.875C1.97446 17.8065 2.06984 17.7674 2.14016 17.6978C2.21049 17.6282 2.25 17.5339 2.25 17.4355C2.25 17.3371 2.21049 17.2427 2.14016 17.1732C2.06984 17.1036 1.97446 17.0645 1.875 17.0645H1.5V14.8387H1.875C1.97446 14.8387 2.06984 14.7996 2.14016 14.7301C2.21049 14.6605 2.25 14.5661 2.25 14.4677C2.25 14.3694 2.21049 14.275 2.14016 14.2054C2.06984 14.1359 1.97446 14.0968 1.875 14.0968H1.5V11.871H1.875C1.97446 11.871 2.06984 11.8319 2.14016 11.7623C2.21049 11.6927 2.25 11.5984 2.25 11.5C2.25 11.4016 2.21049 11.3073 2.14016 11.2377C2.06984 11.1681 1.97446 11.129 1.875 11.129H1.5V8.90323H1.875C1.97446 8.90323 2.06984 8.86414 2.14016 8.79457C2.21049 8.725 2.25 8.63064 2.25 8.53226C2.25 8.43387 2.21049 8.33951 2.14016 8.26994C2.06984 8.20037 1.97446 8.16129 1.875 8.16129H1.5V5.93548H1.875C1.97446 5.93548 2.06984 5.8964 2.14016 5.82683C2.21049 5.75726 2.25 5.6629 2.25 5.56452C2.25 5.46613 2.21049 5.37177 2.14016 5.3022C2.06984 5.23263 1.97446 5.19355 1.875 5.19355H1.5V2.96774H1.875C1.97446 2.96774 2.06984 2.92866 2.14016 2.85909C2.21049 2.78952 2.25 2.69516 2.25 2.59677C2.25 2.49839 2.21049 2.40403 2.14016 2.33446C2.06984 2.26489 1.97446 2.22581 1.875 2.22581H1.5V1.85484C1.5 1.55968 1.61853 1.27661 1.8295 1.0679C2.04048 0.859187 2.32663 0.741935 2.625 0.741935H15.375C15.6734 0.741935 15.9595 0.859187 16.1705 1.0679C16.3815 1.27661 16.5 1.55968 16.5 1.85484V21.1452ZM22.875 2.22581H22.5V1.85484C22.5 1.3629 22.3025 0.891119 21.9508 0.54327C21.5992 0.19542 21.1223 0 20.625 0C20.1277 0 19.6508 0.19542 19.2992 0.54327C18.9475 0.891119 18.75 1.3629 18.75 1.85484V18.9194C18.7503 18.9927 18.7711 19.0646 18.81 19.1271L20.25 21.2639V22.629C20.25 22.7274 20.2895 22.8218 20.3598 22.8913C20.4302 22.9609 20.5255 23 20.625 23C20.7245 23 20.8198 22.9609 20.8902 22.8913C20.9605 22.8218 21 22.7274 21 22.629V21.2639L22.44 19.1271C22.4789 19.0646 22.4997 18.9927 22.5 18.9194V2.96774H22.875C22.9745 2.96774 23.0698 3.00683 23.1402 3.0764C23.2105 3.14597 23.25 3.24032 23.25 3.33871V7.04839C23.25 7.14677 23.2895 7.24113 23.3598 7.3107C23.4302 7.38027 23.5255 7.41935 23.625 7.41935C23.7245 7.41935 23.8198 7.38027 23.8902 7.3107C23.9605 7.24113 24 7.14677 24 7.04839V3.33871C24 3.04355 23.8815 2.76048 23.6705 2.55177C23.4595 2.34306 23.1734 2.22581 22.875 2.22581ZM21.75 18.8081L20.625 20.4774L19.5 18.8081V17.6803C19.8565 17.5334 20.2388 17.4577 20.625 17.4577C21.0112 17.4577 21.3935 17.5334 21.75 17.6803V18.8081ZM21.75 16.8939C21.018 16.6639 20.232 16.6639 19.5 16.8939V13.2287C19.8565 13.0818 20.2388 13.0061 20.625 13.0061C21.0112 13.0061 21.3935 13.0818 21.75 13.2287V16.8939ZM21.75 12.4423C21.018 12.2123 20.232 12.2123 19.5 12.4423V4.45161H21.75V12.4423ZM21.75 3.70968H19.5V1.85484C19.5 1.55968 19.6185 1.27661 19.8295 1.0679C20.0405 0.859187 20.3266 0.741935 20.625 0.741935C20.9234 0.741935 21.2095 0.859187 21.4205 1.0679C21.6315 1.27661 21.75 1.55968 21.75 1.85484V3.70968ZM13.875 2.22581H4.125C4.02554 2.22581 3.93016 2.26489 3.85984 2.33446C3.78951 2.40403 3.75 2.49839 3.75 2.59677V5.56452C3.75 5.6629 3.78951 5.75726 3.85984 5.82683C3.93016 5.8964 4.02554 5.93548 4.125 5.93548H13.875C13.9745 5.93548 14.0698 5.8964 14.1402 5.82683C14.2105 5.75726 14.25 5.6629 14.25 5.56452V2.59677C14.25 2.49839 14.2105 2.40403 14.1402 2.33446C14.0698 2.26489 13.9745 2.22581 13.875 2.22581ZM13.5 5.19355H4.5V2.96774H13.5V5.19355ZM12.375 7.41935H5.625C5.52554 7.41935 5.43016 7.45844 5.35984 7.52801C5.28951 7.59758 5.25 7.69194 5.25 7.79032C5.25 7.88871 5.28951 7.98307 5.35984 8.05264C5.43016 8.12221 5.52554 8.16129 5.625 8.16129H12.375C12.4745 8.16129 12.5698 8.12221 12.6402 8.05264C12.7105 7.98307 12.75 7.88871 12.75 7.79032C12.75 7.69194 12.7105 7.59758 12.6402 7.52801C12.5698 7.45844 12.4745 7.41935 12.375 7.41935Z"
      fill={stroke}
    />
  </Svg>
);
export default JournalIcon;
