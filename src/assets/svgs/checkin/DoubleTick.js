import * as React from 'react';
import Svg, { Rect, Path } from 'react-native-svg';
import { useContext } from 'react';
import { ThemeContext } from '../../../context/ThemeContext';
const DoubleTick = (props) => {
  const { theme } = useContext(ThemeContext);
  return (
    <Svg
      width={65}
      height={65}
      viewBox="0 0 70 70"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={70} height={70} rx={35} fill="#24E1E9" />
      <Path
        d="M45.8653 23.5741C45.1345 22.8309 43.954 22.8309 43.2233 23.5741L32.6554 34.3223L35.2973 37.0093L45.8653 26.2421C46.5773 25.5179 46.5773 24.2983 45.8653 23.5741ZM53.81 23.5551L35.2973 42.3834L28.7767 35.7706C28.046 35.0274 26.8655 35.0274 26.1347 35.7706C25.404 36.5139 25.404 37.7144 26.1347 38.4577L33.967 46.4235C34.6977 47.1668 35.8782 47.1668 36.609 46.4235L56.4519 26.2612C57.1827 25.5179 57.1827 24.3173 56.4519 23.5741H56.4332C55.7212 22.8118 54.5407 22.8118 53.81 23.5551ZM15.5481 38.4767L23.3803 46.4426C24.1111 47.1858 25.2915 47.1858 26.0223 46.4426L27.3339 45.1086L18.1901 35.7706C17.4593 35.0274 16.2788 35.0274 15.5481 35.7706C14.8173 36.5139 14.8173 37.7335 15.5481 38.4767Z"
        fill={theme.colors.text}
      />
    </Svg>
  );
};
export default DoubleTick;
