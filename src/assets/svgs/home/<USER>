import * as React from 'react';
import Svg, { Rect, Defs, Pattern, Use, Image } from 'react-native-svg';
import { ThemeContext } from '../../../context/ThemeContext';
const InahledSvg = (props) => {
  const { theme } = React.useContext(ThemeContext);
  return (
    <Svg
      width={38}
      height={36}
      viewBox="0 0 38 36"
      // fill="none"
      fill={theme.colors.svgColor}
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      {...props}
    >
      <Rect
        width={38}
        height={36}
        fill="url(#pattern0_55288_6238)"
        fillOpacity={0.95}
      />
      <Defs>
        <Pattern
          id="pattern0_55288_6238"
          patternContentUnits="objectBoundingBox"
          width={1}
          height={1}
        >
          <Use
            xlinkHref="#image0_55288_6238"
            transform="matrix(0.00381084 0 0 0.00423554 0.156682 0.0715604)"
          />
        </Pattern>
        <Image
          id="image0_55288_6238"
          width={200}
          height={200}
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEwAACxMBAJqcGAAAEhpJREFUeJztnXu0XHV1xz83rxsgMYQEkgpJLgFD5CXltSDSNkABA0uKgkIUY0CwaH1VobAUFyqWUq1alkpLBQuVl4pLhEorDw0gr1JIwEdAIBkeeRACIW/zurd/7Ht6fr+5M2fmN3PmnDMz389aZ90zM799fnvuOXt+r/3bG4QQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEKIwtGTtwJtxlTguMHz14E7A2SHAx9yXt8K/DFA/l3A5MHzB4EXAmSFyIT5wMDg8WCg7GRHdgDYJVB+kSN7eqCsaJBheSvQZhzinC9tQvY1YGOA7GhgZhN1iwaRgdRPD3Cq8/qhQPm/akL2JKB38Hw98HSgvBAt50TiLs524K0BsmOBtY78hwPrvsuRvSVQVohMuI/4If1ZoOxFjuwaYOcA2YPxxy7HBtYtRMuZhf+Q/nmA7E7ASkf2isC6f+TIPhEoK0Qm/Jz4IX0gUPZTjuxGYPcA2ZlAvyP/3sC6hWg5M/Af0hMDZHuA5x3ZbwTWfbUj+1u0biUKyOXED2no7NGfObLbgSkBsqOw8Uokf05g3UJkwpPED+nnAmWvdGR/ESg7G79rFrqwKFJA6yDJjAQOcl7fHSh/mHMeaiCu7EOELSyKlJCBJLM7MMJ5/YdA+T2d82czlBUpIQNJZnjZ6+1NyGcpK1JCBpLMamwMEDE1UP4153xahrIiJWQgyWzGdyv/i0B5d9ZrdhOyxzC0NROiELhrEfcEyp7myG7AfLLqZRywxZE/ObBuITLhGOKHtB84IEC2F9tYFcl/NrDunziyoTNoQmTGQuIH9aZAWXctZDmx23o9zHZkB4AjA+sWIhPeT/yQ7gDeFiA7GdjkyH88sO6HHdk7AmWFyIRhwGLiB/WaQPmrHNklhE2OzMHv4u0fWLcQmTCfxgfcU4BtjvycwLrd/ehXBcoKkQnlA+4zA+XdAfe/B8qejz+OEaKQXE/8oF4dKHsWfjcrhAnY2CeSnxEoLxpEC4VhPOqcz6xaqrZsH2GzWa8DzzVRt2gQGUgYq5zzcU3I9gBvybBu0SAykDDch3pTE7LNyofKigaRgYShwHFdhgykfhQ4TogEFDhOiAQUOE6IKihwnBAJKHCcEFVQ4DghElDgOCESUOC4LkbrIMkocFyXIwNJRoHjuhwN+pIZAezlvC4Fyu+JtUJgU70hWW13I/a/WouNR4QQQgghhBBCCCG6kzxmsf4JmJhDvSI/LsQi5bcdeRhICYXz7zb2JnyKvBBooVCIBEbULtJSbqBNf1lETS7LW4F2pUTshDc7V01EK3E3mPXlq0rjqIslRAIyECESkIEIkYAMRIgEZCBCJCADESIBGYgQCeS9UCi6g71qF6nJDiwu8ZspXKtuZCAiCx5M8VprsZyN9wA304GR7ktoJb0bGMjg6MdyP7Ys45bGIKKd6cHCsT4FfJ4OCUJSIptfFx3FOfponmFYpJejgEuA31So5yeE5X4sJCXyv2E62s9AKnEKltzUresOYHiL6suEEvnfMB2dYSAAY4Dbyur7x7QunveOwrn46ZFF57DUOW/1jsJhwA+ADwy+HsACh4emuisEJWJLn52rJqKVZNWCRPTiBxp/PI2LahZLdApbgPMw4wA4HPjLZi+qhUKRBbcSFpc4YhuwHMvq9WNgXY3yT2KD9Cij8Hzg3gbqzZUS6mJ1A2kP9N/E0tnVGjef6sisrqN84SghA+kGWjUjdgPJQ4MxWDavqPzbmvkS6mKJLLiIxgLHjcASGH0QmDD43jzsR/ayKjIbsBm0fQdf74utlbQNJdSCdANpzmLtCvy3c72twD4J5R92ys5tpmLNYol24E3M5+r5wdcjgY8llN/qnDfleiIDEe3CJiyuc8RJWVQqAxHthLsyvm/VUikiAxHtxAbnfHQWFcpAsmNq3gqIcGQg2bA/8DQ2XSnaCBlI69kNuBMYB1wHHJGvOiIEGUjr+T4wffC8F/Mpekv14qJIyEBayzxix7mIacA3c9BFNIAMpHWMB77hvH7NOT8XmJWtOqIRZCCt4wvEyUrXY2OPhYOve/CNRxQUGUhrmAR83Hn9VeBF4BPOe0cBc7JUSoQjA2kNnwB2GjxfCXx78Pxh4D+dchdlqZQIRwaSPiOBjzqvvwVsdl5f4ZwfC7w9C6VEY8hA0udkYI/B883A98o+fwQ/oMD8DHQSDSIDSZ8znfPbgTUVytxQpbwoGDKQdBmOP/D+UZVyt2GBl8HWRQ5qpVKicWQg6XIEtvsNLAzN3VXKvYrfzTqhlUqJxpGBpMsxzvmj2CafavyyipwoEDKQdDncOX+4Rln3czkwFhQZSLoc4JwvqlF2oXO+F+aaIgqGDMSYg4WK+QXwz1gkjMmB1+jB3wa6uEb5ZZgLSkRo/KYxWPj/v8dmy34PXB54DVEDxcUynsNC0/QBJw6+FwVAvgm4EXijxjX2wN8GurRaQYcS8QzWVOB/apTvBd4DfAiLOzuq7PMX6qhTBKAWxFiKv9oN1iIcCVwFvAJcTfK22UnO+Qb8/dPVWOGc755Qbizm/PgicAu2GFluHAC/raNOEYAMxNiBPyboL/t8JywO03PYXo5dGYr7Xq3WJsJdRBxX4fMRWDzaJZjD46Syz109t2BpyUSKyEBi3GnXR4BDgK8Bq5z3RwF/CzwDvK9M3u1e1RvJ3G21yqN0HA78L9aCTXTe3wxcDxw/+FnEQ5iRiBSRgcT8zDmfhYXavxjrVv018LLz+SRslfxmrPsDfhTxAerDbQGGOX8vxYz0Hc7nG7BWZApwDrAAOKOK/qKNKVHc2LyLiXX7etlno7FUwxvx484+i+XpPtZ5b0md9d3oyFyKddP+q+z6/cC1xA6QEe/Bj1Vb3v3Km1ZkmOoru241Fjhl5qdUd2aUKK6BfJpYt3XEEcVd+rDumHuj1mB7O9y8FPVwhyPzTXwDHcBi0VZbZX/cKXdrnfVliQykQUoU10DGYA93pN+3qpQbBnwRG9xXymGxg/qm0B+rIj+AdeHGVpE7o6zsYXXUlTUykAYpUVwDAbgQv+tyQELZd2Njg0oPeF8ddS2vINePdbeqsQs23RuV/2kd9eSBDKRBShTbQHqxrk2k42MkJ6Y/Ar/ViY4TE2TAWodyme3UvqHfdspvockMSi2kIwxEs1hD2YIfXOFI4EsJ5R8HjgNeL3v/0Br1HFL2egdwNjaFW42Tgb9xXl9Jm2VPErUpUewWJOJa/G7PKTXKH4Hf3VqMdYeqcQv+r+F5Na4/HTPCqPwiKq+mF4WOaEHyoER7GMgYbEEw0nUtcHANmVMxY4pkrqtS7iP4N/qKKuUixgO/c8pvpPjBHmQgDVKiPQwEbIC+nljfFdTu81+OfxPLY19NwaaQo8/vpnbWVjfn3gBwVsiXyAkZSIOUaB8DAXvAtxHrvIzkma3h2Cp4VL5EHCMLzDU9+mw1yW714zEXEveh+FL4V8gFGUiDlGgvAwFzL3fXPN7AVs6rMRMb7EflvzD4/mz8G3x2wjX68LtVA8B3G9Q/D1phINOda+5IKLcAGUjmzMNPUL8N+GRC+SvxDWoscL/z3gMJssdjwa7dh+w7+P5eRacVBnK8c81XE8otQAaSOcOxgNMDFY712K/9zVhUxT0w9/U1Tplby2RmATtjXsHXYJ67b+AP8qPjlyTPiBWRVhjI951r3plQbgEykMwYgzkpVlr1rnZsA36I72vlHr/HfK/WBVxzA7Zpa0prv25qpG0gp+B3c+cmlHVb6nkp1J0pJdrHQM7AZq7qfYizODZjY5qk1f0ikJaB7I5NTGx1rreI5O//hFP29Cbq1p70KozAXDouKHt/C3AXcA+2e28l9qs2Dpv+fSfmhh6S0XYttpfjV1jLEnkCT8AG+7OxLFWRZ/FobF/ICZgB1+s5nCe/xsZvoYzCZvncsdebWOuRNEif7pwva6DeXClR7BZkJEO7RhuBL1PZ/b2cYdiC4W9IbglexdxGdqp8GY9ebKX9lbJrPAP8SX1fK3Na0Xouwd9EVom3O+WjH6+2okSxDeQ/8G/Ko8DeDVxnBPZLX+lG305jcbDGAj8ou9ZTFDMpaJqGUcI8nOuZqPiiI/d4jbI1URfLZzLmEOhyNfWF8ClnO/BvwPn4uwEHsO5bpajvtViPDe5PwyYPAA7E/MDua+B6WTEX646Gsg1rNV+ss/xoLLhGRLXg4YWmRLFbkKMZusfjOvzACbXoAT6M71zoHluBr2DTvPXSC1yCBYRwr/WxJKEccXWcllGdX3bq3EDYPSsMS4i/RNJqdJ68E1uTcG/yeiyKyOFUX7CbjOUmrDX+iI4V2AzNfgm67INNM79cJrsdP5NVkRiOr2sWU9PH4i/kfjWDOluCu++6yEks98EiHVZ6sN/AVsJ/iAVeuAvbl1Gp7Fp8X65qrcpyrJt0C7bgeA9DjSI6lmF7UIrKGHx9d2txfbOw2a2ovmepb/KjkLj7sIvulTocC+RQ7vZRz7EDG/C/Ff/mHY11r6pt1U06/ojFDi76zMyexDr307o1m2HY5ja327me2tsSCs3Pib/Mp3PWpV52xqZZf4XfGlQ6lmAB52Y48q6BRDsJJ2KRUJ6gsnuJezyFdbNCA2rnxaHEuq+qUbYReoH34y8IDmDT8cenWVEes1juws1eOdTfCJuwHYbXYlOth2KR3Cdi/8N1wEvYCm+9My6rsdhbX8fWVw7DFrjGY2OcNdjs2UKSHfOKiLtQ2kM6LvrDsf/NflgrXD7l+zK2cForAHjhuYTY4m/PWZesqNSCdDJuZJhWH/2YE2NLxjl5tCBu3gwlr+xMsriv64AfY7HLfteqSvIwkKec86hL0ciimSgubiC7+7DFvmbZjhlFCet2PoatJ3Ukq4ibyFrRQjqBbupi7YY/6TAjuXixySsu1q+d81RnHUTuHEe8kPoq8IccdWmavAzkXue8G1qQbsK9n/dWLSUSmYo/E3Fgvuq0nG7pYo3E9xQo+kJwoXHdOK7MWZdW0y0Gcirx99xM9ej0og7cXBwrsV+fTuVpbPalBOyfqyatxd1o1pau5kViAr4PzQfzVUc0yT74QRVOyledzuB64n/oItor7pPw+Vd8b1rdyxQ4CH/O/L35qiMaZBp+JMnz81Wns/gp8T/2GTp7LNKp3ER8D1+k2GkZ2o4D8XeCXZivOiKQY/Cn7M/NV53O5Brif/AGGosiIrJnFH6A7YUoa1lLmIC/a+9+9I9uB76G73Z+dL7qdDZz8Zvqy/JVR9TgXfgTLN/JV53uwI2AvoOhMapEMdgb36XkGcJCGIkGGYefgnkdtUNNimzZFX/csQndo0w5GD/ix3JslVbkzy4MTQknD4gceDf+1O9LWJAEkR+7YFFdXOP4Sq4adTnlqZJX0NmesEVmIhbE270f1+aqkQAsPYA7U7Iey5UhsuMA/HHhABYQT9PwBeFc/O5WP/APKDJ9FpzN0AiQ30WOiIVjDhbj1r1Rj2HJUkT6TMD3r4qm3f8uT6VEMjOxFGXuTdsCXI7m4NOiBzgHP+rMALbmoTWpNmBn4HsMja73ChY7V92uxjkBS0Vd/r+9n/bJrJs6zfYlx2P7kLPuk56CxWEtZynwL5hfl6iPXiyX+FEVPnsSG3P0Z6lQigxg24BzDUzo5oTToaNIx6U0SRp5Gx7AUgIoAJwoEp8Hrmj2Iml2jS5maPiexbQmP4QQEZOwCRyXizFX/MJxEX4T9zxdPMATLWcafs7LAeBzuWpUB5/FV/gF/IQqQqTB3vgZkweAz+SpUAifwVd8KdCXp0Kio5iOBYdwn7FP5apRA3wS/wuU0F5z0Tz74mf/7cd89tqScmfDl9DeDtE4M7BFYdc4LshVoxS4AN9IXkZ7O0Q4+2EJYF3j+GiuGqXI+fhGsow2zzwkMmUmth/INY7zctWoBXwE30iWY78KQiSxPxb9P3pudmAOlR3JOfgRwFcgl3VRnQOxNG6ucczLVaMMmIdvJCuxXWtCuByM73a/HdvA1RWcjb9LcBWdn4ZN1M87gNX4xjE3V41y4AP4RvIa9qshups/xQ9Ktw04My9l8t5bfBZwI7FX8RosXZnoXg7BAghC3HLclp86+fM+7Fci770DOop1bEUJlf6f05GR6IiPrcBpFIC8u1gus9DioTCeBR7JWwkhhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQoqD8H7/aCRP4x+8VAAAAAElFTkSuQmCC"
        />
      </Defs>
    </Svg>
  );
};
export default InahledSvg;
