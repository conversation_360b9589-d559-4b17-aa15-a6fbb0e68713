import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const Sun = ({ color = '#FFD700', ...props }) => (
  <Svg
    width={31}
    height={30}
    viewBox="0 0 31 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M4.89453 15C4.89453 14.6022 4.7365 14.2206 4.45519 13.9393C4.17389 13.658 3.79236 13.5 3.39453 13.5H1.89453C1.49671 13.5 1.11518 13.658 0.833871 13.9393C0.552566 14.2206 0.394531 14.6022 0.394531 15C0.394531 15.3978 0.552566 15.7794 0.833871 16.0607C1.11518 16.342 1.49671 16.5 1.89453 16.5H3.39453C3.79236 16.5 4.17389 16.342 4.45519 16.0607C4.7365 15.7794 4.89453 15.3978 4.89453 15ZM5.85453 22.5L4.78953 23.565C4.51015 23.846 4.35334 24.2262 4.35334 24.6225C4.35334 25.0188 4.51015 25.399 4.78953 25.68C5.07057 25.9594 5.45075 26.1162 5.84703 26.1162C6.24331 26.1162 6.62349 25.9594 6.90453 25.68L7.96953 24.615C8.21527 24.328 8.34368 23.9589 8.3291 23.5814C8.31452 23.2039 8.15802 22.8458 7.89088 22.5787C7.62373 22.3115 7.26563 22.155 6.88811 22.1404C6.5106 22.1259 6.14148 22.2543 5.85453 22.5ZM15.3945 4.5C15.7924 4.5 16.1739 4.34196 16.4552 4.06066C16.7365 3.77936 16.8945 3.39782 16.8945 3V1.5C16.8945 1.10218 16.7365 0.720644 16.4552 0.43934C16.1739 0.158035 15.7924 0 15.3945 0C14.9967 0 14.6152 0.158035 14.3339 0.43934C14.0526 0.720644 13.8945 1.10218 13.8945 1.5V3C13.8945 3.39782 14.0526 3.77936 14.3339 4.06066C14.6152 4.34196 14.9967 4.5 15.3945 4.5ZM23.8845 8.01C24.278 8.00834 24.6551 7.85212 24.9345 7.575L25.9995 6.51C26.1566 6.37553 26.2841 6.21005 26.3741 6.02396C26.4642 5.83786 26.5147 5.63516 26.5227 5.42858C26.5307 5.222 26.4959 5.016 26.4205 4.82352C26.3451 4.63103 26.2307 4.45621 26.0845 4.31002C25.9383 4.16384 25.7635 4.04946 25.571 3.97404C25.3785 3.89863 25.1725 3.86382 24.966 3.8718C24.7594 3.87978 24.5567 3.93038 24.3706 4.02041C24.1845 4.11045 24.019 4.23798 23.8845 4.395L22.8945 5.46C22.6152 5.74104 22.4583 6.12122 22.4583 6.5175C22.4583 6.91378 22.6152 7.29396 22.8945 7.575C23.159 7.83804 23.5119 7.99313 23.8845 8.01ZM5.88453 7.575C6.16392 7.85212 6.54102 8.00834 6.93453 8.01C7.13194 8.01114 7.32763 7.9733 7.51039 7.89866C7.69315 7.82401 7.85937 7.71402 7.99953 7.575C8.27891 7.29396 8.43572 6.91378 8.43572 6.5175C8.43572 6.12122 8.27891 5.74104 7.99953 5.46L6.93453 4.395C6.79566 4.25416 6.6304 4.14205 6.4482 4.06507C6.266 3.98809 6.07043 3.94776 5.87264 3.94636C5.67485 3.94497 5.47872 3.98255 5.29546 4.05695C5.11219 4.13135 4.94537 4.24113 4.80453 4.38C4.66369 4.51887 4.55158 4.68413 4.4746 4.86633C4.39762 5.04853 4.35729 5.2441 4.35589 5.44189C4.35308 5.84135 4.50906 6.22556 4.78953 6.51L5.88453 7.575ZM28.8945 13.5H27.3945C26.9967 13.5 26.6152 13.658 26.3339 13.9393C26.0526 14.2206 25.8945 14.6022 25.8945 15C25.8945 15.3978 26.0526 15.7794 26.3339 16.0607C26.6152 16.342 26.9967 16.5 27.3945 16.5H28.8945C29.2924 16.5 29.6739 16.342 29.9552 16.0607C30.2365 15.7794 30.3945 15.3978 30.3945 15C30.3945 14.6022 30.2365 14.2206 29.9552 13.9393C29.6739 13.658 29.2924 13.5 28.8945 13.5ZM24.9345 22.5C24.6496 22.3415 24.3207 22.2802 23.9977 22.3253C23.6748 22.3705 23.3753 22.5196 23.1447 22.7502C22.9141 22.9808 22.765 23.2802 22.7198 23.6032C22.6747 23.9261 22.736 24.255 22.8945 24.54L23.9595 25.605C24.2406 25.8844 24.6208 26.0412 25.017 26.0412C25.4133 26.0412 25.7935 25.8844 26.0745 25.605C26.3539 25.324 26.5107 24.9438 26.5107 24.5475C26.5107 24.1512 26.3539 23.771 26.0745 23.49L24.9345 22.5ZM15.3945 6.75C13.7628 6.75 12.1678 7.23385 10.8111 8.14038C9.45437 9.0469 8.39695 10.3354 7.77252 11.8429C7.1481 13.3504 6.98472 15.0092 7.30305 16.6095C7.62138 18.2098 8.40712 19.6798 9.5609 20.8336C10.7147 21.9874 12.1847 22.7732 13.785 23.0915C15.3854 23.4098 17.0442 23.2464 18.5517 22.622C20.0592 21.9976 21.3476 20.9402 22.2542 19.5835C23.1607 18.2267 23.6445 16.6317 23.6445 15C23.6406 12.8132 22.7701 10.7171 21.2238 9.17075C19.6775 7.62443 17.5813 6.75397 15.3945 6.75ZM15.3945 20.25C14.3562 20.25 13.3411 19.9421 12.4778 19.3652C11.6144 18.7883 10.9415 17.9684 10.5442 17.0091C10.1468 16.0498 10.0428 14.9942 10.2454 13.9758C10.448 12.9574 10.948 12.0219 11.6822 11.2877C12.4164 10.5535 13.3519 10.0534 14.3703 9.85088C15.3887 9.6483 16.4443 9.75227 17.4036 10.1496C18.3629 10.547 19.1829 11.2199 19.7597 12.0833C20.3366 12.9466 20.6445 13.9616 20.6445 15C20.6445 16.3924 20.0914 17.7277 19.1068 18.7123C18.1223 19.6969 16.7869 20.25 15.3945 20.25ZM15.3945 25.5C14.9967 25.5 14.6152 25.658 14.3339 25.9393C14.0526 26.2206 13.8945 26.6022 13.8945 27V28.5C13.8945 28.8978 14.0526 29.2794 14.3339 29.5607C14.6152 29.842 14.9967 30 15.3945 30C15.7924 30 16.1739 29.842 16.4552 29.5607C16.7365 29.2794 16.8945 28.8978 16.8945 28.5V27C16.8945 26.6022 16.7365 26.2206 16.4552 25.9393C16.1739 25.658 15.7924 25.5 15.3945 25.5Z"
      fill={color}
      stroke="none"
    />
  </Svg>
);

export default Sun;
