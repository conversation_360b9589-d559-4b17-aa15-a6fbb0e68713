import React from 'react';
import { View, Text, Button } from 'react-native';
import { useSubscription } from '../context/SubscriptionContext';
import { useNavigation } from '@react-navigation/native';

const withSubscription = (WrappedComponent, requiredSubscription) => {
  return (props) => {
    const { subscription, loading } = useSubscription();
    const navigation = useNavigation();

    if (loading) {
      return (
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <Text>Loading...</Text>
        </View>
      );
    }

    const hasSubscription = subscription && subscription.productId !== 'free';

    if (!hasSubscription) {
      return (
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <Text>This feature requires a subscription.</Text>
          <Button
            title="Upgrade Now"
            onPress={() => navigation.navigate('BuyYPD')}
          />
        </View>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

export default withSubscription;
