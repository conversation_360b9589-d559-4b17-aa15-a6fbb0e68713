import { useState, useContext } from 'react';
import { View, ScrollView, Modal, SafeAreaView } from 'react-native';
import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import styles from '../../assets/styles/SafetyModal';
import { Fonts, isSmallDevice } from '../../utils';
import Logo from '../../assets/svgs/profileBuilder/Logo';
import { resetLoadingState } from '../../utils';
import { ThemeContext } from '../../context/ThemeContext';
import { ypdBlack, ypdWhite } from '../../utils/colors';

const SafetyModal = ({ visible, onConfirm, onDecline }) => {
  const { theme } = useContext(ThemeContext);

  const [loading, setIsLoading] = useState(false);

  resetLoadingState(setIsLoading);

  const safetyConditions = [
    'I am not pregnant or nursing.',
    'I do not have a personal history of schizophrenia.',
    'To my knowledge, I am not allergic to cannabis products.',
    'I am not taking blood thinners like coumadin, heparin, eligis, xarelto, or others.',
    'If I have diabetes mellitus or heart problems, I will check with my health care provider before starting cannabis.',
  ];

  const handleConfirm = () => {
    setIsLoading(true);
    onConfirm();
  };

  const handleDecline = () => {
    setIsLoading(true);
    onDecline();
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Modal visible={visible} animationType="slide">
        <View
          style={[
            styles.safetyModalContainer,
            { backgroundColor: theme.colors.background },
          ]}
        >
          <ScrollView contentContainerStyle={styles.modalContent}>
            <View style={styles.modalIconContainer}>
              <Logo height={80} width={80} />
              <Heading
                text="Your safety comes first!"
                fontSize={28}
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.headingStyle}>
              <Heading
                text="In order to improve your safety while using YPD, please read and confirm the following statements:"
                size="sm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.conditionContainer}>
              {safetyConditions.map((condition, index) => (
                <View key={index}>
                  <Heading
                    text={`• ${condition}`}
                    fontFamily={Fonts.REGULAR}
                    size="sm"
                    color={theme.colors.text}
                  />
                </View>
              ))}
            </View>
            <View style={styles.headingStyle}>
              <Heading
                text="Please confirm that all of the above statements are true."
                fontWeight={800}
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
              />
            </View>
            <View
              style={
                isSmallDevice ? styles.buttonWrapperSmall : styles.buttonWrapper
              }
            >
              <CustomButton
                title="I don’t confirm"
                onPress={handleDecline}
                width={150}
                variant="stepsgreenoutline"
                color={theme.dark ? ypdWhite : ypdBlack}
                disabled={loading}
              />
              <CustomButton
                title="I confirm"
                onPress={handleConfirm}
                width={150}
                disabled={loading}
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default SafetyModal;
