import { useCallback, useState, useEffect, useContext } from 'react';
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Linking,
  BackHandler,
} from 'react-native';
import Heading from '../../../components/Heading';
import { Fonts, responsiveStyles } from '../../../utils';
import { useDispatch, useSelector } from 'react-redux';
import { setDosageType } from '../../../redux/slices/profileSlice';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import CustomButton from '../../../components/CustomButton';
import ProgressBar from '../progressBar';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { ypdWhite } from '../../../utils/colors';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import { resetLoadingState } from '../../../utils';
import { updateUserProfile } from '../../../api/profile';
import { recordEvent } from '../../../api/events';
import { ThemeContext } from '../../../context/ThemeContext';
import BottomSheet from '../../../components/BottomSheet';

const Step7 = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const route = useRoute();
  const navigation = useNavigation();
  const { profileBuilding = true, marginTop = 0 } = route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  resetLoadingState(setIsLoading);

  const profileData = useSelector((state) => state.profile);
  const dosageType = useSelector((state) => state.profile.dosageType);
  const [oldDosageType] = useState(dosageType || 'both');

  useEffect(() => {
    const backAction = () => {
      if (dosageType === '' || isLoading) {
        return true;
      }
      dispatch(setDosageType(oldDosageType));
      navigation.goBack();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [isLoading, dispatch, navigation, oldDosageType]);

  const toggleState = (e, state) => {
    e.preventDefault();
    dispatch(setDosageType(state));
  };

  const handleReadMorePress = () => {
    Linking.openURL('https://yourperfectdose.com/guide-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const handleNextPress = async () => {
    const createdAt = new Date().toISOString();
    setIsLoading(true);
    if (!profileBuilding) {
      try {
        await updateUserProfile(profileData);
        const attributes = {
          currentScreen: 'dosageType',
          screenTime: (new Date() - screenTime) / 1000,
        };
        await recordEvent(
          'Profile Event',
          profileData.username,
          true,
          '#P3',
          attributes,
          createdAt,
        );
        navigation.navigate('Tabs', { screen: 'Home' });
      } catch (error) {
        console.error('Error updating profile:', error);
      } finally {
        setIsLoading(false);
      }
    } else {
      const attributes = {
        currentScreen: 'dosageType',
        screenTime: (new Date() - screenTime) / 1000,
      };
      await recordEvent(
        'Profile Building Event',
        profileData.username,
        true,
        '#PB9',
        attributes,
        createdAt,
      );
      navigation.navigate('NotificationPermission');
    }
  };

  const handleBackPress = () => {
    dispatch(setDosageType(oldDosageType));
    navigation.goBack();
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaView
        style={[
          styles.profileBuilderContainer,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <View style={{ flex: 1 }}>
          <View style={[styles.stickyWrapper, { marginTop }]}>
            <View
              style={[
                styles.navigationButtonPosition,
                {
                  top: profileBuilding
                    ? responsiveStyles.buttonPosition
                    : responsiveStyles.buttonPosition - 80,
                },
              ]}
            ></View>
            <View style={{ flex: 1 }}>
              {profileBuilding && (
                <View style={styles.progressBar}>
                  <ProgressBar step={7} />
                </View>
              )}
              <ScrollView
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={[
                  profileBuilding
                    ? { paddingTop: responsiveStyles.progressBarPaddingTop }
                    : { paddingTop: 20 },
                ]}
              >
                <View style={styles.profileBuilderScrollContainer}>
                  <View style={styles.container}>
                    <View>
                      <View>
                        <Heading
                          text="How do you prefer to consume cannabis?"
                          size="lg"
                          fontFamily={Fonts.MEDIUM}
                          color={theme.colors.text}
                        />
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            marginTop: 130,
                          }}
                          pointerEvents={isLoading ? 'none' : 'auto'}
                        >
                          <CustomButton
                            title="Both"
                            width="30%"
                            height={42}
                            borderRadius={8}
                            variant={
                              dosageType === 'both'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              dosageType === 'both'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'both')}
                          />
                          <CustomButton
                            title="Oral"
                            width="30%"
                            height={42}
                            borderRadius={8}
                            variant={
                              dosageType === 'oral'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              dosageType === 'oral'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'oral')}
                          />
                          <CustomButton
                            title="Inhaled"
                            width="30%"
                            height={42}
                            borderRadius={8}
                            variant={
                              dosageType === 'inhaled'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              dosageType === 'inhaled'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'inhaled')}
                          />
                        </View>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={toggleDetails}
                      style={styles.toggleContainer}
                      disabled={isLoading}
                    >
                      <Heading
                        text="Select our guide"
                        size="sm"
                        fontFamily={Fonts.MEDIUM}
                        color={theme.colors.link}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </ScrollView>
              <View style={styles.bottomButton}>
                <ProfileBuilderButton
                  continueText="Next"
                  onContinuePress={handleNextPress}
                  continueDisabled={isLoading}
                  activityIndicator={isLoading}
                  backDisabled={isLoading}
                  onBackPress={handleBackPress}
                  onlyNextButton={profileBuilding}
                />
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>

      <BottomSheet visible={showDetails} onClose={() => setShowDetails(false)}>
        <ScrollView style={styles.bottomSheetContainer}>
          <View style={{ height: 10 }} />
          <View
            style={[
              styles.cardContainer,
              { backgroundColor: theme.colors.lightTextGray },
              { borderColor: theme.colors.stepCards },
              { borderWidth: 1 },
            ]}
          >
            <Heading
              text="Two methods, one goal: relief:"
              size="md"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text="Combining oral and inhaled cannabis offers more flexibility and control in relieving your symptoms."
              size="sm"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.headerWrapper}>
            <View style={styles.left}>
              <Heading
                text="Oral"
                size="md"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.textBlack}
              />
            </View>
            <View style={styles.right}>
              <Heading
                text="Inhaled"
                size="md"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.textBlack}
              />
            </View>
          </View>
          <View style={styles.contentWrapper}>
            <View style={styles.textContainerTHC}>
              <View style={styles.listStyleType}>
                <Heading
                  text="• "
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
                <Heading
                  text="Slower onset"
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
              </View>
              <View style={styles.listStyleType}>
                <Heading
                  text="• "
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
                <Heading
                  text="Lasts for hours"
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
              </View>
            </View>
            <View style={styles.textContainerCBD}>
              <View style={styles.listStyleType}>
                <Heading
                  text="• "
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
                <Heading
                  text="Works more rapidly"
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
              </View>
              <View style={styles.listStyleType}>
                <Heading
                  text="• "
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
                <Heading
                  text="Great for flareups"
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  fontSize={14}
                  color={theme.colors.text}
                />
              </View>
            </View>
          </View>

          <View style={styles.customBottomButton}>
            <View pointerEvents={isLoading ? 'none' : 'auto'}>
              <CustomButton
                title="Read more about these methods"
                variant="teal"
                alignSelf="center"
                width="95%"
                textStyle={{ color: ypdWhite }}
                color={ypdWhite}
                onPress={handleReadMorePress}
              />
            </View>
          </View>
        </ScrollView>
      </BottomSheet>
    </GestureHandlerRootView>
  );
};

export default Step7;
