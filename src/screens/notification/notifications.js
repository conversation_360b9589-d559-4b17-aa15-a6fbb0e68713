import { useState, useEffect, useContext, useCallback } from 'react';
import { View, Switch, Platform, ScrollView, SafeAreaView } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { Shadow } from 'react-native-shadow-2';

import { setShowNotifications } from '../../redux/slices/profileSlice';
import {
  setDailyReminders,
  setEveningReminder,
  setMorningReminder,
} from '../../redux/slices/notificationSlice';
import LocalNotificationService from '../../utils/localNotifactionService';
import { updateUserProfile } from '../../api/profile';
import { recordEvent } from '../../api/events';
import {
  ypdBlack,
  ypdGreen,
  ypdLightRed,
  ypdOldGreen,
  ypdRed,
  ypdTeal,
  ypdText<PERSON><PERSON>,
  ypd<PERSON>hite,
} from '../../utils/colors';
import { Fonts } from '../../utils';

import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import BackButton from '../../components/BackButton';
import IconMaterial from 'react-native-vector-icons/Ionicons';
import { ThemeContext } from '../../context/ThemeContext';

import styles from '../../assets/styles/NotificationAlert.scss';
import LinearGradient from 'react-native-linear-gradient';

const Notifications = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const gradientColors = [ypdTeal, ypdGreen];

  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );
  const dailyReminders = useSelector(
    (state) => state.notifications.dailyReminders,
  );
  const profileData = useSelector((state) => state.profile);

  const morningReminder = useSelector(
    (state) => state.notifications.morningReminder,
  );
  const eveningReminder = useSelector(
    (state) => state.notifications.eveningReminder,
  );

  const now = new Date();
  const formatReminderTime = (reminder) =>
    reminder?.hour !== null && reminder?.min !== null
      ? new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          reminder.hour,
          reminder.min,
        )
      : null;

  const morningTime = formatReminderTime(morningReminder);
  const eveningTime = formatReminderTime(eveningReminder);

  const [screenTime, setScreenTime] = useState(null);
  const [showDayTimePicker, setShowDayTimePicker] = useState(false);
  const [showNightTimePicker, setShowNightTimePicker] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  useEffect(() => {
    if (showNotifications) {
      LocalNotificationService.requestPermission(true);
      LocalNotificationService.cancelNotification('1');
      LocalNotificationService.cancelNotification('2');
      if (dailyReminders) {
        LocalNotificationService.scheduleDailyReminder('morning');
        LocalNotificationService.scheduleDailyReminder('evening');
      }
    } else {
      LocalNotificationService.cancelAllLocalNotifications();
    }
  }, []);

  const handleToggleReminders = () => {
    LocalNotificationService.cancelNotification('1');
    LocalNotificationService.cancelNotification('2');
    if (!dailyReminders) {
      LocalNotificationService.scheduleDailyReminder('morning');
      LocalNotificationService.scheduleDailyReminder('evening');
    }
    dispatch(setDailyReminders(!dailyReminders));
    recordEvent(
      'Notifications Event',
      profileData.username,
      true,
      '#N2',
      {
        currentScreen: 'notifications',
        screenTime: (new Date() - screenTime) / 1000,
      },
      new Date().toISOString(),
    );
    setScreenTime(new Date());
  };

  const handleToggleNotifications = async () => {
    dispatch(setShowNotifications(!showNotifications));
    if (!showNotifications) {
      if (dailyReminders) {
        LocalNotificationService.scheduleDailyReminder('morning');
        LocalNotificationService.scheduleDailyReminder('evening');
      }
    } else {
      LocalNotificationService.cancelAllLocalNotifications();
    }

    const updatedProfile = {
      ...profileData,
      showNotifications: !showNotifications,
    };

    try {
      updateUserProfile(updatedProfile);
      recordEvent(
        'Notifications Event',
        profileData.username,
        true,
        '#N1',
        {
          currentScreen: 'time spent',
          screenTime: (new Date() - screenTime) / 1000,
        },
        new Date().toISOString(),
      );
      setScreenTime(new Date());
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const handleTimeConfirm = (type) => (selectedDate) => {
    const hour = selectedDate.getHours();
    const min = selectedDate.getMinutes();
    const setter = type === 'morning' ? setMorningReminder : setEveningReminder;
    const notifId = type === 'morning' ? '1' : '2';

    dispatch(setter({ hour, min }));
    type === 'morning'
      ? setShowDayTimePicker(false)
      : setShowNightTimePicker(false);
    LocalNotificationService.cancelNotification(notifId);
    LocalNotificationService.scheduleDailyReminder(type);
  };

  const ReminderCard = ({ type, title, subtitle, time, onPress }) => (
    <View
      style={[
        styles.timeCard,
        {
          backgroundColor: theme.colors.cardbackground,
          borderColor: theme.colors.border,
        },
      ]}
    >
      <View style={styles.timeRow}>
        <View style={styles.timeInfo}>
          <View style={styles.timeIconContainer}>
            <IconMaterial
              name={type === 'morning' ? 'sunny' : 'moon'}
              size={22}
            />
          </View>
          <View>
            <Heading text={title} size="sm" color={theme.colors.text} />
            <Heading
              text={subtitle}
              size="xsmall"
              style={styles.timeSubtitle}
              color={theme.colors.text}
            />
          </View>
        </View>
        <CustomButton
          title={time?.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
          onPress={onPress}
          variant="green"
          style={styles.timeButton}
          textStyle={styles.timeText}
          fontFamily={Fonts.REGULAR}
          fontSize={16}
          textColor={ypdWhite}
          width={80}
          height={35}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <LinearGradient colors={gradientColors} style={styles.gradientCircle}>
            <IconMaterial name="notifications" size={40} color={ypdWhite} />
          </LinearGradient>
          <Heading
            text="Manage your notification/reminder preferences"
            size="sm"
            color={theme.colors.text}
            style={styles.headerSubtitle}
          />
        </View>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {[
          {
            label: 'Notifications',
            value: showNotifications,
            onToggle: handleToggleNotifications,
          },
          {
            label: 'Daily Reminders',
            value: dailyReminders,
            onToggle: handleToggleReminders,
            hidden: !showNotifications,
          },
        ]
          .filter((item) => !item.hidden)
          .map(({ label, value, onToggle }, index) => (
            <View
              key={label}
              style={[
                styles.mainCard,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <View style={styles.switchRow}>
                <Heading
                  text={label}
                  size="md"
                  style={styles.mainTitle}
                  color={theme.colors.text}
                />
                <Switch
                  trackColor={{ false: ypdTextGrey, true: ypdOldGreen }}
                  thumbColor={ypdWhite}
                  style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                  onValueChange={onToggle}
                  value={value}
                />
              </View>
            </View>
          ))}

        {showNotifications && dailyReminders && (
          <View style={styles.timeSection}>
            <Heading
              text="Reminders Schedule"
              size="lg"
              style={styles.sectionHeader}
              color={theme.colors.text}
            />

            <ReminderCard
              type="morning"
              title="Morning Reminder"
              subtitle="Start your day"
              time={morningTime}
              onPress={() => setShowDayTimePicker(true)}
            />
            {showDayTimePicker && (
              <DateTimePickerModal
                isVisible={showDayTimePicker}
                mode="time"
                date={morningTime}
                onConfirm={handleTimeConfirm('morning')}
                onCancel={() => setShowDayTimePicker(false)}
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                textColor={theme === 'dark' ? ypdBlack : ypdTextGrey}
                backgroundColor={ypdWhite}
                pickerContainerStyleIOS={{
                  alignSelf: 'center',
                  width: '100%',
                  alignItems: 'center',
                  backgroundColor: ypdWhite,
                }}
                isDarkModeEnabled={theme === 'dark'}
                style={{
                  backgroundColor: ypdWhite,
                }}
              />
            )}

            <ReminderCard
              type="evening"
              title="Evening Reminder"
              subtitle="End your day"
              time={eveningTime}
              onPress={() => setShowNightTimePicker(true)}
            />
            {showNightTimePicker && (
              <DateTimePickerModal
                isVisible={showNightTimePicker}
                mode="time"
                date={eveningTime}
                onConfirm={handleTimeConfirm('evening')}
                onCancel={() => setShowNightTimePicker(false)}
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                textColor={theme === 'dark' ? ypdBlack : ypdTextGrey}
                backgroundColor={ypdWhite}
                pickerContainerStyleIOS={{
                  alignSelf: 'center',
                  width: '100%',
                  alignItems: 'center',
                  backgroundColor: ypdWhite,
                }}
                isDarkModeEnabled={theme === 'dark'}
              />
            )}
          </View>
        )}

        {!showNotifications && (
          <Heading
            text="Enable notifications to stay on track with reminders and check-in lockouts."
            size="sm"
            color={ypdRed}
            style={styles.warningText}
            textAlign="center"
          />
        )}
        {showNotifications && !dailyReminders && (
          <Heading
            text="Don't miss a checkin—turn on on daily reminders."
            size="sm"
            color={ypdLightRed}
            style={styles.warningText}
            textAlign="center"
          />
        )}
      </ScrollView>

      <View style={styles.backButton}>
        <BackButton onBackPress={() => navigation.navigate('Settings')} />
      </View>
    </SafeAreaView>
  );
};

export default Notifications;
