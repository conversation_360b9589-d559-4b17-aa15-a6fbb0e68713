import { <PERSON>ing, <PERSON><PERSON>reaView, <PERSON><PERSON>View, View } from 'react-native';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import { getMonday, getStartOfWeek } from '../../utils/utils';
import styles from '../../assets/styles/HomeScreen';
import CustomButton from '../../components/CustomButton';
import { ThemeContext } from '../../context/ThemeContext';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect, useRoute } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import {
  setSuggestions,
  setStreakDates,
  setCBDILastSuggestion,
  setCBDOLastSuggestion,
  setLastSuggestion,
  setTHCILastSuggestion,
  setTHCOLastSuggestion,
  setCheckinStats,
  setAllowCheckin,
} from '../../redux/slices/suggestionSlice';
import LocalNotificationService from '../../utils/localNotifactionService';
import { getSuggestions } from '../../api/suggestion';
import SuggestionPopup from '../../components/SuggestionPopup';
import DoseSuggestionCard from '../../components/DoseSuggestionCard';
import WhereToBuy from '../../assets/svgs/home/<USER>';
import { checkLockout } from '../../utils/suggestions';
import { ypdWhite } from '../../utils/colors';
import { Geofencing } from '../../utils/geofencing';
import { useGeo } from '../../context/GeoContext';

const HomeScreen = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const route = useRoute();
  const { newNote = false } = route.params || {};
  const { newSuggestion = false } = route.params || {};

  const [journalPopup, setJournalPopup] = useState(newNote);
  const [lastCheckinMap, setLastCheckinMap] = useState({});
  const [suggestionPopup, setSuggestionPopup] = useState(newSuggestion);

  const profile = useSelector((state) => state.profile);
  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );
  const dailyReminders = useSelector(
    (state) => state.notifications.dailyReminders,
  );
  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion || {},
  );
  const userId = profile.userId;

  useEffect(() => {
    if (showNotifications) {
      LocalNotificationService.requestPermission(true);
      LocalNotificationService.cancelNotification('1');
      LocalNotificationService.cancelNotification('2');
      if (dailyReminders) {
        LocalNotificationService.scheduleDailyReminder('morning');
        LocalNotificationService.scheduleDailyReminder('evening');
      }
    } else {
      LocalNotificationService.cancelAllLocalNotifications();
    }
  }, []);

  useEffect(() => {
    dispatch(setAllowCheckin(checkLockout(lastSuggestion, lastCheckinMap)));
  }, [lastCheckinMap]);

  useEffect(() => {
    const interval = setInterval(() => {
      dispatch(setAllowCheckin(checkLockout(lastSuggestion, lastCheckinMap)));
    }, 300000);

    return () => clearInterval(interval);
  }, [lastSuggestion]);

  useFocusEffect(
    useCallback(() => {
      const fetchSuggestions = async () => {
        console.log('Fetching suggestions.');

        const streakDates = new Set();

        try {
          let thci = null;
          let thco = null;
          let cbdi = null;
          let cbdo = null;

          const today = new Date();
          const todayStr = today.toISOString().split('T')[0];
          const weekStart = getStartOfWeek(today);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);

          let dailySuggestions = 0;
          let weeklySuggestions = 0;
          let streakSuggestions = new Set();

          let dailyCheckin = 0;
          let weeklyCheckin = 0;
          let streakCheckin = new Set();

          let dailyJournalEntries = 0;
          let weeklyJournalEntries = 0;
          let streakJournalEntries = new Set();

          const lastCheckins = {
            thci: null,
            thco: null,
            cbdi: null,
            cbdo: null,
          };

          const suggestions = await getSuggestions(userId);

          dispatch(setSuggestions(suggestions));

          for (const suggestion of suggestions) {
            if (suggestion.suggestionType !== 'journal') {
              const { cannabisType, dosageType } = suggestion;

              if (['both', 'thc'].includes(cannabisType)) {
                if (
                  !thci &&
                  ['both', 'inhaled'].includes(dosageType) &&
                  !thci
                ) {
                  thci = suggestion;
                }
                if (!thco && ['both', 'oral'].includes(dosageType) && !thco) {
                  thco = suggestion;
                }
              }

              if (['both', 'cbd'].includes(cannabisType)) {
                if (
                  !cbdi &&
                  ['both', 'inhaled'].includes(dosageType) &&
                  !cbdi
                ) {
                  cbdi = suggestion;
                }
                if (!cbdo && ['both', 'oral'].includes(dosageType) && !cbdo) {
                  cbdo = suggestion;
                }
              }
            }

            if (suggestion.checkinAt) {
              const checkinDate = new Date(suggestion.checkinAt);
              const checkinStr = checkinDate.toISOString().split('T')[0];

              const isInThisWeek =
                checkinDate >= weekStart && checkinDate <= weekEnd;

              if (['initial', 'regular'].includes(suggestion.suggestionType)) {
                if (checkinStr === todayStr) dailySuggestions++;
                if (isInThisWeek) weeklySuggestions++;
                if (isInThisWeek) streakSuggestions.add(checkinStr);
              }

              if (suggestion.suggestionType === 'journal') {
                if (checkinStr === todayStr) dailyJournalEntries++;
                if (isInThisWeek) weeklyJournalEntries++;
                if (isInThisWeek) streakJournalEntries.add(checkinStr);
              }

              if (suggestion.suggestionType === 'checkin') {
                const modality = JSON.parse(suggestion.checkinType)['M'][
                  'modality'
                ]['S'];
                if (!lastCheckins[modality]) {
                  lastCheckins[modality] = checkinDate;
                }

                if (checkinStr === todayStr) dailyCheckin++;
                if (isInThisWeek) weeklyCheckin++;
                if (isInThisWeek) streakCheckin.add(checkinStr);
              }

              streakDates.add(checkinStr);
            }
          }

          const dailyTotal = Math.max(
            dailyCheckin,
            dailySuggestions,
            dailyJournalEntries,
          );

          const weeklyTotal = Math.max(
            weeklyCheckin,
            weeklySuggestions,
            weeklyJournalEntries,
          );

          const streakTotal = Math.max(
            streakCheckin.size,
            streakSuggestions.size,
            streakJournalEntries.size,
          );

          const streakByWeek = {};

          for (const dateStr of streakDates) {
            const mondayStr = getMonday(dateStr);

            if (!streakByWeek[mondayStr]) {
              streakByWeek[mondayStr] = new Set();
            }

            streakByWeek[mondayStr].add(dateStr);
          }

          let fullStreakWeeks = 0;

          for (const dateSet of Object.values(streakByWeek)) {
            if (dateSet.size === 7) {
              fullStreakWeeks++;
            }
          }

          dispatch(
            setCheckinStats({
              dailyCheckins: dailyTotal,
              weeklyCheckins: weeklyTotal,
              weeklyStreaks: streakTotal,
              fullStreakWeeks,
            }),
          );

          const excludedTypes = ['checkin', 'journal'];
          const lastSuggestion =
            suggestions.find(
              (s) => !excludedTypes.includes(s.suggestionType),
            ) || {};

          dispatch(
            setAllowCheckin(checkLockout(lastSuggestion, lastCheckinMap)),
          );

          // console.log('lastSuggestion', lastSuggestion);
          // console.log('streakDates', streakDates);
          // console.log('lastCheckins', lastCheckins);

          setLastCheckinMap(lastCheckins);
          dispatch(setStreakDates(Array.from(streakDates)));

          dispatch(setLastSuggestion(lastSuggestion));
          dispatch(setTHCILastSuggestion(thci || lastSuggestion));
          dispatch(setTHCOLastSuggestion(thco || lastSuggestion));
          dispatch(setCBDILastSuggestion(cbdi || lastSuggestion));
          dispatch(setCBDOLastSuggestion(cbdo || lastSuggestion));

          if (lastSuggestion.userDisabled || lastSuggestion.isBELimitExceeded) {
            setJournalPopup(false);
            setSuggestionPopup(false);
            navigation.navigate('MaxModalities');
          }
        } catch (err) {
          console.error('Error fetching suggestions:', err);
          dispatch(setSuggestions([]));
        }
      };

      if (userId) {
        fetchSuggestions();
      }
    }, [userId, dispatch]),
  );

  const handleFeelingPress = () => {
    navigation.navigate('Progress', {
      screen: 'OverallEffect',
    });
  };

  const handleSuggestionPopup = () => {
    setSuggestionPopup(false);
  };

  const handleJournalPopup = () => {
    setJournalPopup(false);
  };

  const isNewProfile =
    (new Date() - new Date(profile?.createdAt)) / (1000 * 60 * 60) <= 1;

  const { geoStatus, setGeoStatus } = useGeo();

  if (geoStatus !== 'allowed') {
    return <Geofencing onStatusChange={setGeoStatus} />;
  }

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View>
          <View style={styles.headingContent}>
            {isNewProfile ? (
              <>
                <Heading
                  text="Welcome!"
                  size="lg"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <View style={styles.content}>
                  <Heading
                    text={`Your starting doses are ready.\nKeep track of each dose, how \nyou’re feeling, report any side effects—this helps the app tailor your dosage to what works best for you.`}
                    size="md"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              </>
            ) : (
              <>
                <Heading
                  text={`Hi, ${profile?.name}! ` || 'Hello'}
                  size="lg"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <View style={styles.content}>
                  <Heading
                    text="Keep track of each dose, how you’re feeling, report any side effects."
                    size="md"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              </>
            )}
          </View>
          <DoseSuggestionCard lastCheckinMap={lastCheckinMap} />
          <View style={styles.letUsKnowButton}>
            <CustomButton
              title="Let us know how you are feeling!"
              variant={theme.dark ? 'letUsKnow' : 'purple'}
              fontFamily={Fonts.MEDIUM}
              textColor={ypdWhite}
              onPress={handleFeelingPress}
              width="92%"
            />
          </View>
          <View
            style={[
              styles.whereToBuyContainer,
              { backgroundColor: theme.colors.whereToByBG },
            ]}
          >
            <View style={styles.whereToBuyText}>
              <Heading
                text="Where to Buy"
                size="sm"
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
              />
              <Heading
                text="Find products near you."
                size="xssmall"
                color={theme.colors.link}
                style={{
                  textDecorationLine: 'underline',
                  textDecorationColor: theme.colors.link,
                }}
                onPress={() => {
                  Linking.openURL('https://yourperfectdose.com/buy-cann').catch(
                    (err) => console.error('Failed to open URL', err),
                  );
                }}
              />
            </View>
            <View>
              <WhereToBuy />
            </View>
          </View>
          <SuggestionPopup
            visible={suggestionPopup}
            onClose={handleSuggestionPopup}
            isClose={false}
            data={[
              'Use your best judgment to decide how you use, alter, or ignore this suggestion.',
              'Give any cannabis by mouth 2 hours to work.',
              'Give any inhaled cannabis 1 hour to work.',
              'If you experience any problems or side effects, call 911 or go to your nearest hospital emergency room.',
            ]}
          />
          <SuggestionPopup
            visible={journalPopup}
            onClose={handleJournalPopup}
            isClose={false}
            data={[
              'Thanks for recording how you are feeling.',
              'Your data has been saved successfully in your journal.',
              'You can view it in the progress section.',
              'If you experience any problems or side effects, call 911 or go to your nearest hospital emergency room.',
            ]}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
