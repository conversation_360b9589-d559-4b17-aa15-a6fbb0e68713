import { useState, useContext } from 'react';
import { View, ScrollView, Modal, SafeAreaView } from 'react-native';
import styles from '../../assets/styles/SafetyModal';
import { Fonts, isSmallDevice, resetState } from '../../utils';
import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import Logo from '../../assets/svgs/profileBuilder/Logo';
import { useNavigation } from '@react-navigation/native';
import uuid from 'react-native-uuid';
import { createDoseSuggestion } from '../../api/suggestion';
import { useSelector } from 'react-redux';
import { signOut } from 'aws-amplify/auth';
import { ThemeContext } from '../../context/ThemeContext';

const MaxModalities = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [loading, setLoading] = useState(false);
  const [quitLoading, setQuitLoading] = useState(false);

  const profile = useSelector((state) => state.profile);

  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion,
  );

  const thci = useSelector(
    (state) => state.suggestions.thciLastSuggestion.thci,
  );
  const thco = useSelector(
    (state) => state.suggestions.thcoLastSuggestion.thco,
  );
  const cbdi = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion.cbdi,
  );
  const cbdo = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion.cbdo,
  );

  const handlePress = async (userQuits = false) => {
    userQuits ? setQuitLoading(true) : setLoading(true);

    const suggestionData = {
      rthci: thci,
      rthco: thco,
      rcbdi: cbdi,
      rcbdo: cbdo,
      id: uuid.v4(),
      userId: profile.userId,
      username: profile.username,
      dosageType: profile.dosageType,
      cannabisType: profile.cannabisType,
      hitCBDMax: lastSuggestion?.hitCBDMax,
      hitTHCMax: lastSuggestion?.hitTHCMax,
      loopN: (lastSuggestion?.loopN ?? 0) + 1,
      suggestionType: lastSuggestion?.suggestionType,
      isBELimitExceeded: lastSuggestion?.isBELimitExceeded,
      ...(userQuits && { userQuits: true }),
    };

    await createDoseSuggestion(suggestionData);
    await new Promise((resolve) => setTimeout(resolve, 3000));

    if (userQuits) {
      await resetState(signOut);
      navigation.reset({
        index: 0,
        routes: [{ name: 'MainIntro' }],
      });
    } else {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tabs',
            state: {
              index: 0,
              routes: [
                {
                  name: 'Home',
                  params: { newSuggestion: true },
                },
              ],
            },
          },
        ],
      });
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Modal visible={true} animationType="slide">
        <View
          style={[
            styles.safetyModalContainer,
            { backgroundColor: theme.colors.background },
          ]}
        >
          <ScrollView contentContainerStyle={styles.modalContent}>
            <View style={styles.modalIconContainer}>
              <Logo height={80} width={80} />
              <Heading
                text="Your safety comes first!"
                fontSize={28}
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.headingStyle}>
              {lastSuggestion.userDisabled ? (
                <Heading
                  text="We are sorry, but YPD can no longer help you. We suggest that you contact a health care provider to explore options in reaching your goals."
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  color={theme.colors.text}
                />
              ) : (
                <>
                  <Heading
                    text={`You have hit the limit of the amount of ${
                      lastSuggestion.hitCBDMax && lastSuggestion.hitTHCMax
                        ? 'THC and CBD'
                        : lastSuggestion.hitTHCMax
                          ? 'THC'
                          : lastSuggestion.hitCBDMax
                            ? 'CBD'
                            : ''
                    } that YPD is comfortable suggesting.`}
                    size="sm"
                    fontFamily={Fonts.REGULAR}
                    color={theme.colors.text}
                  />
                  <View style={styles.divider} />
                  <Heading
                    text={`We can try to adjust your ${
                      lastSuggestion.hitCBDMax && lastSuggestion.hitTHCMax
                        ? 'THC and CBD'
                        : lastSuggestion.hitTHCMax
                          ? 'THC'
                          : lastSuggestion.hitCBDMax
                            ? 'CBD'
                            : 'dosage'
                    } suggestion to work on your issue.`}
                    size="sm"
                    color={theme.colors.text}
                    fontFamily={Fonts.REGULAR}
                  />
                </>
              )}
            </View>
            {!lastSuggestion.userDisabled && (
              <View style={styles.headingStyle}>
                <Heading
                  text="Would you like to try adjusting your dose?"
                  fontWeight={800}
                  fontFamily={Fonts.BOLD}
                  color={theme.colors.text}
                />
              </View>
            )}

            <View
              style={[
                isSmallDevice
                  ? styles.buttonWrapperSmall
                  : styles.buttonWrapper,
                lastSuggestion.userDisabled && { justifyContent: 'center' },
              ]}
            >
              <CustomButton
                title="Quit YPD"
                onPress={() => handlePress(true)}
                width={150}
                variant={lastSuggestion.userDisabled ? 'green' : 'greenoutline'}
                disabled={loading || quitLoading}
                activityIndicator={quitLoading}
              />
              {!lastSuggestion.userDisabled && (
                <CustomButton
                  title="Adjust Dose"
                  onPress={() => handlePress()}
                  width={150}
                  disabled={loading || quitLoading}
                  activityIndicator={loading}
                />
              )}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default MaxModalities;
