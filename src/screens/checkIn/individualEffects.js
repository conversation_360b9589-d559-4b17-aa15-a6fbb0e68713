import { useState, useEffect, useCallback, useContext } from 'react';
import {
  View,
  Dimensions,
  Text,
  Platform,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { Slider } from '@react-native-assets/slider';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import { Fonts } from '../../utils';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import { setEffects, setNotes } from '../../redux/slices/checkinSlice';
import WarningModal from '../profileBuilding/warningModal';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import styles from '../../assets/styles/IndividualEffects.scss';
import {
  activeGradientColors,
  ypdDefaultGreyGradient,
  ypdGreen,
  ypdBlack,
} from '../../utils/colors';
import UnHappy from '../../assets/svgs/checkin/UnHappy';
import Struggling from '../../assets/svgs/checkin/Struggling';
import Mild from '../../assets/svgs/checkin/Mild';
import NotGreat from '../../assets/svgs/checkin/NotGreat';
import Empty from '../../assets/svgs/checkin/Empty';
import Smiley from '../../assets/svgs/checkin/Smiley';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Input from '../../components/Input';

const defaultGreyGradient = [ypdDefaultGreyGradient, ypdDefaultGreyGradient];

const IndividualEffects = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const storedProblems = useSelector((state) => state.profile.problems);
  const notes = useSelector((state) => state.checkIn.notes);
  const problemLevel = useSelector((state) => state.checkIn.problemLevel);

  const problems =
    Array.isArray(storedProblems) && storedProblems.length > 0
      ? storedProblems
      : ['Anxiety', 'Pain'];

  const initialLevels = problems.reduce((acc, key, index) => {
    if (index === 0) {
      acc[key] = problemLevel?.[key] ?? 1;
    } else {
      acc[key] = problemLevel?.[key] ?? 0;
    }
    return acc;
  }, {});

  const [levels, setLevels] = useState(initialLevels);
  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [notesModal, setNotesModal] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  const sliderWidth = Dimensions.get('window').width * 0.8;
  const segmentCount = 5;
  const gapWidth = 5;
  const totalGapsWidth = (segmentCount - 1) * gapWidth;
  const segmentWidth = (sliderWidth - totalGapsWidth) / segmentCount;

  const profileData = useSelector((state) => state.profile);

  useEffect(() => {
    dispatch(setEffects(levels));
  }, [levels]);

  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => setIsLoading(false), 1000);
      return () => clearTimeout(timeout);
    }
  }, [isLoading]);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveDosingEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'individualEffects',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Dosing Event',
      profileData.username,
      true,
      '#D2',
      attributes,
      createdAt,
    );
  };

  const handleContinuePress = async () => {
    if (Object.values(levels).some((val) => val === 0)) {
      setWarningMessage(
        'Please tell us how are you feeling about all the problems',
      );
      setWarningModalVisible(true);
      return;
    }
    setIsLoading(true);
    await saveDosingEvent();
    navigation.navigate('LastDose');
  };

  const handleSideEffects = async () => {
    if (Object.values(levels).some((val) => val === 0)) {
      setWarningMessage(
        'Please tell us how are you feeling about all the problems',
      );
      setWarningModalVisible(true);
      return;
    }
    setIsLoading(true);
    await saveDosingEvent();
    navigation.navigate('SideEffects');
  };

  const renderCustomTrack = (value) => (
    <View style={[styles.segmentedTrackContainer, { width: sliderWidth }]}>
      {Array.from({ length: segmentCount }).map((_, index) => {
        const isActive = value > index;
        const colors = isActive
          ? activeGradientColors[index]
          : defaultGreyGradient;

        return (
          <LinearGradient
            key={index}
            colors={colors}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[
              styles.segmentBlock,
              {
                width: segmentWidth,
                marginRight: index < segmentCount - 1 ? gapWidth : 0,
                opacity: 1,
              },
            ]}
          />
        );
      })}
    </View>
  );

  const handleSliderChange = (value, key) => {
    setLevels((prev) => ({ ...prev, [key]: value }));
  };

  const isAnySliderActive = Object.values(levels).some((val) => val > 0);

  const getThumbIconComponent = (value) => {
    switch (value) {
      case 0:
        return Empty;
      case 1:
        return UnHappy;
      case 2:
        return Struggling;
      case 3:
        return NotGreat;
      case 4:
        return Mild;
      case 5:
        return Smiley;
      default:
        return null;
    }
  };

  const renderCustomMark = (markValue, selectedValue) => {
    const markLabels = {
      0: 'Slide',
      1: 'The worst!',
      2: 'Really struggling',
      3: 'Not great',
      4: 'Mild discomfort',
      5: 'All good!',
    };

    const isSelected = selectedValue === markValue;

    return (
      <View
        style={{
          width: 150,
          height: 30,
          marginTop: 55,
          ...(isSelected && { alignItems: 'center' }),
        }}
      >
        {isSelected && (
          <Text style={[styles.sliderText, { color: theme.colors.text }]}>
            {markLabels[markValue] ?? markValue.toString()}
          </Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.container}>
        <View style={styles.headerIcons}>
          <Heading
            text="What seems to be the problem?"
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <CustomButton
            title="Report a side effect"
            variant="outline"
            onPress={handleSideEffects}
            width={179}
            height={42}
            disabled={isLoading}
          />
        </View>
        <View
          style={styles.sliders}
          pointerEvents={isLoading ? 'none' : 'auto'}
        >
          {problems.map((key) => {
            const label = key;
            const value = levels[key] ?? 0;

            return (
              <View key={key} style={styles.sliderContainer}>
                <Heading
                  text={`How is your ${label}?`}
                  size="small"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <View style={styles.sliderWithCustomTrack}>
                  {renderCustomTrack(value)}
                  <Slider
                    style={[styles.slider, { width: sliderWidth }]}
                    minimumValue={0}
                    maximumValue={5}
                    step={1}
                    value={value}
                    onValueChange={(newValue) =>
                      handleSliderChange(newValue, key)
                    }
                    CustomMark={({ value: markValue }) =>
                      renderCustomMark(markValue, value)
                    }
                    CustomThumb={getThumbIconComponent(value)}
                    minimumTrackTintColor="transparent"
                    maximumTrackTintColor="transparent"
                  />
                </View>
              </View>
            );
          })}
        </View>
        <TouchableOpacity onPress={() => setNotesModal(true)}>
          <Heading
            text="Add some additional notes"
            size="small"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>
      <View
        style={[
          styles.buttonContainer,
          Platform.OS === 'android' && styles.buttonContainerAndroid,
        ]}
      >
        <ProfileBuilderButton
          continueText="Next"
          backDisabled={isLoading}
          activityIndicator={isLoading}
          onBackPress={() => navigation.navigate('OverallEffect')}
          onContinuePress={handleContinuePress}
          continueButtonStyle={styles.continueButton}
          continueDisabled={!isAnySliderActive || isLoading}
        />
      </View>

      <Modal
        visible={notesModal}
        animationType="none"
        transparent
        onRequestClose={() => setNotesModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.cardBackground },
            ]}
          >
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setNotesModal(false)}
            >
              <Ionicons
                name="close"
                size={24}
                color={theme.colors.text}
              />
            </TouchableOpacity>

            <Heading
              text="Add additional details"
              size="small"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Input
              value={notes}
              onChangeText={(value) => {
                dispatch(setNotes(value));
              }}
              placeholder="Include anything you'd like to note"
              style={[styles.textArea, notes ? styles.highlightedBorder : null]}
              height={100}
              borderRadius={10}
              multiline={true}
              textAlignVertical="top"
              hasValue={!!notes}
            />

            <View style={styles.modalButtons}>
              <CustomButton
                title="Save"
                width="50%"
                height={42}
                disabled={!notes}
                onPress={() => {
                  setNotesModal(false);
                }}
              />
            </View>
          </View>
        </View>
      </Modal>

      <WarningModal
        visible={warningModalVisible}
        onClose={() => setWarningModalVisible(false)}
        text={warningMessage}
        icon="alert"
        size={30}
        iconColor="red"
      />
    </SafeAreaView>
  );
};

export default IndividualEffects;
