import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ScrollView,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import * as RNIap from 'react-native-iap';
import { ThemeContext } from '../../context/ThemeContext';
import { useSubscription } from '../../context/SubscriptionContext';

const { width } = Dimensions.get('window');

const ypdGreen = '#18F1C7';
const ypdTeal = '#1b2936';

const SUBSCRIPTION_SKUS = Platform.select({
  ios: ['ypd_basic', 'ypd_standard', 'ypd_premium_month'],
  android: ['ypd_free'],
});

const BuyYPD = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { theme } = useContext(ThemeContext);
  const { subscription, loading: subscriptionLoading } = useSubscription();

  useEffect(() => {
    let purchaseUpdateSubscription;
    let purchaseErrorSubscription;

    const initializeInAppPurchases = async () => {
      try {
        await RNIap.initConnection();

        if (Platform.OS === 'android') {
          await RNIap.flushFailedPurchasesCachedAsPendingAndroid();
        }

        await fetchAvailableSubscriptions();

        setupPurchaseListeners();
      } catch (initError) {
        console.error('IAP Initialization Error:', initError);
        setError('Could not initialize in-app purchases');
        setLoading(false);
      }
    };

    const setupPurchaseListeners = () => {
      purchaseUpdateSubscription = RNIap.purchaseUpdatedListener(
        async (purchase) => {
          try {
            console.log('Purchase Update:', JSON.stringify(purchase, null, 2));
            const verificationResult = await verifyPurchase(purchase);

            if (verificationResult) {
              await RNIap.finishTransaction({
                purchase,
                isConsumable: false,
              });

              Alert.alert(
                'Purchase Successful',
                `Subscribed to ${purchase.productId}`,
              );
            } else {
              Alert.alert(
                'Verification Failed',
                'Purchase could not be verified',
              );
            }
          } catch (error) {
            console.error('Purchase Processing Error:', error);
            setError('Failed to process purchase');
          }
        },
      );

      purchaseErrorSubscription = RNIap.purchaseErrorListener((error) => {
        console.warn('Purchase Error:', error);
        setError(error.message || 'Unknown purchase error');
        Alert.alert(
          'Purchase Error',
          error.message || 'An unexpected error occurred',
        );
      });
    };

    initializeInAppPurchases();

    return () => {
      if (purchaseUpdateSubscription) {
        purchaseUpdateSubscription.remove();
      }
      if (purchaseErrorSubscription) {
        purchaseErrorSubscription.remove();
      }
      RNIap.endConnection();
    };
  }, []);

  const fetchAvailableSubscriptions = async () => {
    try {
      setLoading(true);
      const availableSubscriptions = await RNIap.getSubscriptions({
        skus: SUBSCRIPTION_SKUS,
      });

      console.log(
        'Available Subscriptions:',
        JSON.stringify(availableSubscriptions, null, 2),
      );

      const formattedSubscriptions = availableSubscriptions.map((sub) => ({
        productId: sub.productId,
        title: sub.title || 'Subscription',
        description: sub.description || 'No description available',
        price: sub.price,
        currency: sub.currency,
      }));

      setSubscriptions(formattedSubscriptions);
      setLoading(false);
    } catch (fetchError) {
      console.error('Subscription Fetch Error:', fetchError);
      setError('Could not retrieve subscriptions');
      setLoading(false);
    }
  };

  const purchaseSubscription = async (productId) => {
    try {
      if (Platform.OS === 'android') {
        const subscriptions = await RNIap.getSubscriptions({
          skus: SUBSCRIPTION_SKUS,
        });

        const targetSubscription = subscriptions.find(
          (sub) => sub.productId === productId,
        );

        if (!targetSubscription) {
          throw new Error('Subscription not found');
        }

        const purchase = await RNIap.requestSubscription({
          subscriptionOffers: [
            {
              sku: targetSubscription.productId,
              offerToken:
                targetSubscription.subscriptionOfferDetails[0].offerToken,
            },
          ],
        });

        console.log('Android Subscription purchased:', purchase);
      } else {
        const purchase = await RNIap.requestSubscription({
          sku: productId,
        });

        console.log('iOS Subscription purchased:', purchase);
      }
    } catch (error) {
      console.error('Purchase Error:', error);
      Alert.alert(
        'Purchase Failed',
        error instanceof Error ? error.message : 'Unknown error occurred',
      );
    }
  };

  const verifyPurchase = async (purchase) => {
    console.log('Verifying purchase:', JSON.stringify(purchase, null, 2));
    return true;
  };

  if (loading || subscriptionLoading) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <ActivityIndicator size="large" color={ypdGreen} />
        <Text style={styles.loadingText}>Loading Subscriptions...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Text style={styles.title}>Choose Your Plan</Text>
          <Text style={styles.subtitle}>
            Unlock premium features and take your experience to the next level
          </Text>
        </View>

        {/* Error Message */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>⚠️ {error}</Text>
          </View>
        )}

        {/* Subscription Cards */}
        <View style={styles.subscriptionsContainer}>
          {subscription && subscription.productId === 'free' && (
            <View style={[styles.subscriptionCard, styles.featuredCard]}>
              <View style={styles.cardContent}>
                <Text style={styles.subscriptionTitle}>Free Plan</Text>
                <Text style={styles.subscriptionDescription}>
                  You are currently on the Free Plan.
                </Text>
              </View>
            </View>
          )}

          {subscriptions.map((subscription, index) => (
            <View
              key={subscription.productId}
              style={[
                styles.subscriptionCard,
                index === 0 && styles.featuredCard,
              ]}
            >
              {index === 0 && (
                <View style={styles.popularBadge}>
                  <Text style={styles.popularText}>MOST POPULAR</Text>
                </View>
              )}

              <View style={styles.cardContent}>
                <Text style={styles.subscriptionTitle}>
                  {subscription.title}
                </Text>
                <Text style={styles.subscriptionDescription}>
                  {subscription.description}
                </Text>

                <View style={styles.priceSection}>
                  <Text style={styles.priceAmount}>{subscription.price}</Text>
                  <Text style={styles.pricePeriod}>/ month</Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.subscribeButton,
                    index === 0 && styles.featuredButton,
                  ]}
                  onPress={() => purchaseSubscription(subscription.productId)}
                  activeOpacity={0.8}
                >
                  <Text
                    style={[
                      styles.subscribeButtonText,
                      index === 0 && styles.featuredButtonText,
                    ]}
                  >
                    Subscribe Now
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Footer Info */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            • Cancel anytime {'\n'}• Secure payment {'\n'}• Instant access to
            all features
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ypdTeal,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: ypdGreen,
    fontWeight: '600',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 32,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 20,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 59, 48, 0.15)',
    marginHorizontal: 24,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.3)',
  },
  errorText: {
    color: '#FF3B30',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
  },
  subscriptionsContainer: {
    paddingHorizontal: 24,
  },
  subscriptionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 20,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  featuredCard: {
    backgroundColor: 'rgba(24, 241, 199, 0.1)',
    borderColor: ypdGreen,
    borderWidth: 2,
    transform: [{ scale: 1.02 }],
  },
  popularBadge: {
    backgroundColor: ypdGreen,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  popularText: {
    color: ypdTeal,
    fontSize: 12,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  cardContent: {
    padding: 24,
  },
  subscriptionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subscriptionDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    lineHeight: 20,
    marginBottom: 20,
  },
  priceSection: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 24,
  },
  priceAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: ypdGreen,
  },
  pricePeriod: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.6)',
    marginLeft: 4,
  },
  subscribeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  featuredButton: {
    backgroundColor: ypdGreen,
    borderColor: ypdGreen,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  featuredButtonText: {
    color: ypdTeal,
  },
  footer: {
    marginTop: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default BuyYPD;
