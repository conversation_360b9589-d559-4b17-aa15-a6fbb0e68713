import { useState, useContext } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Modal,
  Linking,
  SafeAreaView,
} from 'react-native';
import { getDevicePlatform } from '../../utils/utils';
import styles from '../../assets/styles/AboutUs.scss';
import Heading from '../../components/Heading';
import Help from '../../assets/svgs/myProfile/Help';
import Person from '../../assets/svgs/myProfile/Person';
import Inquiries from '../../assets/svgs/Inquiries';
import CustomButton from '../../components/CustomButton';
import { Shadow } from 'react-native-shadow-2';
import {
  ypdGreen,
  ypdOldGreen,
  ypdShadow,
  ypdTeal,
  ypdWhite,
} from '../../utils/colors';
import BackButton from '../../components/BackButton';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../../context/ThemeContext';

const openEmail = (email) => {
  const url = `mailto:${email}`;
  Linking.openURL(url)
    .then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log('Email client is not supported.');
      }
    })
    .catch((err) => console.error('Error opening email client:', err));
};

const AboutUs = () => {
  const navigation = useNavigation();
  const [contactUsModalVisible, setContactModalVisible] = useState(false);
  const { theme } = useContext(ThemeContext);

  const toggleContactUsModal = () => {
    setContactModalVisible(!contactUsModalVisible);
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'space-evenly',
          padding: 20,
        }}
      >
        <View>
          <View style={styles.heroSection}>
            <View style={styles.feelingWrapper}>
              <Heading
                text="Start feeling "
                size="lg"
                color={ypdTeal}
                fontWeight="bold"
                textAlign="center"
              />
              <Heading
                text="like yourself"
                size="lg"
                color={ypdWhite}
                fontWeight="bold"
                textAlign="center"
              />
              <Heading
                text=" again."
                size="lg"
                color={ypdTeal}
                fontWeight="bold"
                textAlign="center"
              />
            </View>
          </View>
          <View style={styles.gap}>
            <Shadow
              distance={1}
              startColor={ypdShadow}
              offset={[0, 0]}
              stretch={true}
            >
              <View
                style={[
                  styles.aboutCard,
                  { backgroundColor: theme.colors.lightTextGray },
                ]}
              >
                <Heading
                  text="About Us"
                  size="md"
                  color={ypdGreen}
                  fontWeight="bold"
                  textAlign="center"
                />
                <Heading
                  text="YPD is the first mobile app to deliver personalized cannabis dose suggestions to help you reach your wellness goals."
                  size="sm"
                  color={theme.colors.text}
                  textAlign="center"
                  style={{ lineHeight: 24 }}
                />
              </View>
            </Shadow>
          </View>

          <View style={styles.gap}>
            <Shadow
              distance={1}
              startColor={ypdShadow}
              offset={[0, 0]}
              stretch={true}
            >
              <View
                style={[
                  styles.aboutCard,
                  { backgroundColor: theme.colors.lightTextGray },
                ]}
              >
                <Heading
                  text="Release Info"
                  size="md"
                  color={theme.colors.svgFillUnselected}
                  fontWeight="bold"
                  textAlign="center"
                />
                <Heading
                  text={`App Version: ${process.env.RELEASE}`}
                  size="sm"
                  color={ypdOldGreen}
                  textAlign="center"
                />
                <Heading
                  text={`Environment: ${process.env.ENV}`}
                  size="sm"
                  color={ypdOldGreen}
                  textAlign="center"
                />
                <Heading
                  text={`Platform: ${getDevicePlatform()}`}
                  size="sm"
                  color={ypdOldGreen}
                  textAlign="center"
                />
              </View>
            </Shadow>
          </View>

          <CustomButton
            title="Contact Us"
            variant="newGreen"
            onPress={toggleContactUsModal}
            width="100%"
          />
        </View>

        <View style={styles.backButton}>
          <BackButton onBackPress={() => navigation.navigate('Settings')} />
        </View>
      </ScrollView>

      <Modal
        animationType="fade"
        transparent={true}
        visible={contactUsModalVisible}
        onRequestClose={toggleContactUsModal}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.background },
            ]}
          >
            <Heading
              text="Contact Information"
              size="lg"
              fontWeight="bold"
              textAlign="center"
              style={[styles.gapDown, { color: theme.colors.text }]}
            />
            <View>
              <View style={[styles.box]}>
                <Help style={styles.icon} />
                <View>
                  <Heading
                    text="General Help"
                    size="md"
                    color={theme.colors.text}
                    fontWeight="bold"
                  />
                  <TouchableOpacity
                    onPress={() => openEmail('<EMAIL>')}
                  >
                    <Heading
                      text="<EMAIL>"
                      size="link"
                      color={ypdOldGreen}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={[styles.box]}>
                <Person style={styles.icon} />
                <View>
                  <Heading
                    text="Signup Beta"
                    size="md"
                    color={theme.colors.text}
                    fontWeight="bold"
                  />
                  <TouchableOpacity
                    onPress={() => openEmail('<EMAIL>')}
                  >
                    <Heading
                      text="<EMAIL>"
                      size="link"
                      color={ypdOldGreen}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={[styles.box]}>
                <Inquiries style={styles.icon} />
                <View>
                  <Heading
                    text="General Inquiries"
                    size="md"
                    color={theme.colors.text}
                    fontWeight="bold"
                  />
                  <TouchableOpacity
                    onPress={() => openEmail('<EMAIL>')}
                  >
                    <Heading
                      text="<EMAIL>"
                      size="link"
                      color={ypdOldGreen}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View style={styles.closeButton}>
              <CustomButton
                title="Close"
                variant="newGreen"
                onPress={toggleContactUsModal}
                width="50%"
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default AboutUs;
