import { useState, useContext } from 'react';
import {
  View,
  Alert,
  Modal,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Input from '../../components/Input';
import { setAge, setGender, setName } from '../../redux/slices/profileSlice';
import { updateUserProfile } from '../../api/profile';
import CustomButton from '../../components/CustomButton';
import { ThemeContext } from '../../context/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import {
  ypdWhite,
  ypdGreen,
  ypdTeal,
  ypdRed,
  ypdBlack,
} from '../../utils/colors';
import BackButton from '../../components/BackButton';
import { useNavigation } from '@react-navigation/native';
import Heading from '../../components/Heading';
import styles from '../../assets/styles/ManageAccount.scss';
import { ages, Fonts, genders, resetState } from '../../utils';
import { signOut } from 'aws-amplify/auth';
import { deleteAccount } from '../../api/deleteRequests';

const ManageAccount = () => {
  const { theme } = useContext(ThemeContext);
  const colors = theme.colors;

  const dispatch = useDispatch();
  const navigation = useNavigation();
  const gradientColors = [ypdTeal, ypdGreen];

  const profile = useSelector((state) => state.profile);

  const [age, setLocalAge] = useState(profile.age);
  const [name, setLocalName] = useState(profile.name);
  const [gender, setLocalGender] = useState(profile.gender);
  const [loading, setLoading] = useState(false);
  const [deleteRequestModal, setDeleteRequestModal] = useState(false);
  const [deleteRequestComment, setDeleteRequestComment] = useState(false);

  const handleDeleteAccount = async (e) => {
    e.preventDefault();
    setLoading(true);
    setDeleteRequestModal(false);
    const params = {
      userId: profile.userId,
      username: profile.username,
      email: profile.email,
      comment: deleteRequestComment,
      isVerified: true,
      deleteData: true,
      deleteAccount: true,
      source: 'app',
      createdAt: new Date().toISOString(),
    };
    try {
      await deleteAccount(params);
      await resetState(signOut);
      navigation.reset({
        index: 0,
        routes: [{ name: 'MainIntro' }],
      });
    } catch (err) {
      console.error('Error deleting user account:', err);
      Alert.alert('Error', 'Failed to delete account. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      dispatch(setName(name));
      dispatch(setAge(age));
      dispatch(setGender(gender));

      await updateUserProfile({ ...profile, name, age, gender });
    } catch (err) {
      console.error('Error updating profile:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View style={styles.mainContainer}>
          <View style={styles.content}>
            <View style={styles.iconCard}>
              <LinearGradient
                colors={gradientColors}
                style={styles.gradientCircle}
              >
                <Icon name="person" size={35} color={ypdWhite} />
              </LinearGradient>
              <Heading
                text="Manage your Account"
                size="md"
                fontFamily={Fonts.BOLD}
                style={{ color: colors.text }}
              />
            </View>

            <View style={styles.inputGroup}>
              <Heading
                text={profile.email}
                size="sm"
                style={{ color: colors.text }}
              />
            </View>

            <Heading
              text="Name"
              size="sm"
              fontFamily={Fonts.MEDIUM}
              style={{ color: colors.text }}
            />
            <View style={styles.labelsGap} />
            <Input
              value={name}
              onChangeText={setLocalName}
              placeholder="Enter your name"
              placeholderTextColor={colors.placeholderTextColor}
            />
            <View style={styles.gap} />
            <View style={styles.gap} />
            <Heading
              text="Age"
              size="sm"
              fontFamily={Fonts.MEDIUM}
              style={{ color: colors.text }}
            />
            <View style={styles.labelsGap} />
            <View style={styles.optionRow}>
              {ages.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    age === option && styles.selectedOption,
                  ]}
                  onPress={() => setLocalAge(option)}
                >
                  <Heading
                    text={option}
                    size="xsmall"
                    style={[
                      styles.optionText,
                      age === option && styles.selectedOptionText,
                      { color: colors.text },
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>
            <View style={styles.gap} />
            <Heading
              text="Gender"
              size="sm"
              fontFamily={Fonts.MEDIUM}
              style={{ color: colors.text }}
            />
            <View style={styles.labelsGap} />
            <View style={styles.optionRow}>
              {genders.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    gender === option && styles.selectedOption,
                  ]}
                  onPress={() => setLocalGender(option)}
                >
                  <Heading
                    text={option}
                    size="xsmall"
                    style={[
                      styles.optionText,
                      gender === option && styles.selectedOptionText,
                      { color: colors.text },
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>
            <View style={styles.buttonContainer}>
              <CustomButton
                title="Update Profile"
                onPress={handleConfirm}
                activityIndicator={loading}
                disabled={loading}
                color={ypdWhite}
                width="100%"
              />
              <CustomButton
                title="Delete Account"
                onPress={() => setDeleteRequestModal(true)}
                variant="delete"
                color={ypdWhite}
                width="100%"
                disabled={loading}
              />
            </View>
          </View>

          <View style={styles.backButton}>
            <BackButton
              onBackPress={() => navigation.navigate('Settings')}
              disabled={loading}
            />
          </View>
        </View>
        <Modal animationType="fade" transparent visible={deleteRequestModal}>
          <View style={styles.modalOverlay}>
            <View
              style={[styles.modalView, { backgroundColor: colors.background }]}
            >
              <View style={styles.modalContent}>
                <Heading
                  text="Tell us why are you leaving?"
                  size="md"
                  color={theme.colors.text}
                  fontFamily={Fonts.BOLD}
                  textAlign="center"
                />
                <Input
                  value={deleteRequestComment}
                  onChangeText={setDeleteRequestComment}
                  placeholder="Help us improve YPD..."
                  textAlignVertical="top"
                  hasValue={!!deleteRequestComment}
                  disabled={loading}
                  height={100}
                  borderRadius={10}
                  multiline={true}
                  style={styles.textArea}
                />

                <Heading
                  text="Warning: This action will permanently delete all your YDP information."
                  size="sm"
                  color={ypdRed}
                  fontFamily={Fonts.REGULAR}
                  textAlign="center"
                />
                <View style={styles.modalButtonContent}>
                  <CustomButton
                    title="Cancel"
                    onPress={() => {
                      setDeleteRequestComment('');
                      setDeleteRequestModal(false);
                    }}
                    width={130}
                    variant="green"
                  />

                  <CustomButton
                    title="Confirm"
                    onPress={handleDeleteAccount}
                    disabled={!deleteRequestComment}
                    width={130}
                    variant="delete"
                    color={deleteRequestComment ? ypdWhite : ypdBlack}
                    activityIndicator={loading}
                  />
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ManageAccount;
