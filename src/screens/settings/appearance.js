import { useState, useContext, useCallback, useEffect } from 'react';
import { View, ScrollView, Switch } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import IconMaterial from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance as RNAppearance } from 'react-native';
import BackButton from '../../components/BackButton';
import Heading from '../../components/Heading';
import ThemeCard from '../../components/ThemeCard';
import { ThemeContext } from '../../context/ThemeContext';
import styles from '../../assets/styles/Appearance.scss';
import {
  ypdGreen,
  ypdOldGreen,
  ypdTeal,
  ypdTextGrey,
  ypdWhite,
} from '../../utils/colors';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import {
  setSelectedTheme,
  setSystemDefault,
} from '../../redux/slices/appearanceSlice';
import { useFocusEffect } from '@react-navigation/native';
import { recordEvent } from '../../api/events';
import { loadTheme } from '../../utils/theme/themeLoader';

const Appearance = () => {
  const dispatch = useDispatch();
  const { theme, setTheme } = useContext(ThemeContext);
  const gradientColors = [ypdTeal, ypdGreen];

  const [screenTime, setScreenTime] = useState(null);

  const systemDefault = useSelector((state) => state.appearance.systemDefault);
  const selectedTheme = useSelector((state) => state.appearance.selectedTheme);
  const profileData = useSelector((state) => state.profile);

  useEffect(() => {
    loadTheme().then((theme) => {
      dispatch(setSelectedTheme(theme.themeType));
    });
  }, []);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const handleThemeSelection = async (themeType) => {
    setTheme(themeType);
    dispatch(setSelectedTheme(themeType));
    if (themeType === 'system') {
      dispatch(setSystemDefault(true));
      await AsyncStorage.removeItem('user-theme');
    } else {
      dispatch(setSystemDefault(false));
      await AsyncStorage.setItem('user-theme', themeType);
    }
    recordEvent(
      'Theme Event',
      profileData.username,
      true,
      themeType === 'light' ? '#T1' : themeType === 'dark' ? '#T2' : '#T3',
      {
        currentScreen: 'time spent',
        screenTime: (new Date() - screenTime) / 1000,
      },
      new Date().toISOString(),
    );
    setScreenTime(new Date());
  };

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={{ gap: 10 }}>
          <View>
            <View style={[styles.themeCard]}>
              <View style={styles.cardContent}>
                <View style={styles.themeInfo}>
                  <View style={styles.iconWrapper}>
                    <LinearGradient
                      colors={gradientColors}
                      style={styles.gradientCircle}
                    >
                      <IconMaterial
                        name="brightness-6"
                        size={40}
                        color={ypdWhite}
                      />
                    </LinearGradient>
                    <Heading
                      text="Select your preferred theme"
                      size="sm"
                      style={{ color: theme.colors.text, marginTop: 10 }}
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.container}>
            <View style={styles.themesContainer}>
              <ThemeCard
                theme={theme}
                themeType="light"
                title="Light"
                description="Best for bright environments"
                isSelected={selectedTheme === 'light'}
                onPress={() => handleThemeSelection('light')}
                iconName="sunny"
              />
              <ThemeCard
                theme={theme}
                themeType="dark"
                title="Dark"
                description="Best for low-light environments"
                isSelected={selectedTheme === 'dark'}
                onPress={() => handleThemeSelection('dark')}
                iconName="moon"
              />
            </View>

            <View
              style={[
                styles.systemDefaultContainer,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <Heading
                text="System Default"
                size="md"
                style={[styles.systemDefaultText, { color: theme.colors.text }]}
              />
              <Switch
                value={systemDefault}
                onValueChange={(value) => {
                  if (value) {
                    handleThemeSelection('system');
                  } else {
                    const currentSystemTheme = RNAppearance.getColorScheme();
                    handleThemeSelection(
                      currentSystemTheme === 'dark' ? 'dark' : 'light',
                    );
                  }
                }}
                trackColor={{ false: ypdTextGrey, true: ypdOldGreen }}
                thumbColor={ypdWhite}
                style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
              />
            </View>

            <View style={styles.bottomPadding} />
            <View style={styles.backButton}>
              <BackButton />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Appearance;
