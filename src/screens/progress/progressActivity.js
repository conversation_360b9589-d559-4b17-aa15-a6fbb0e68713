import {
  View,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useContext, useState } from 'react';
import {
  stopColor1,
  ypdBlue,
  ypdGreen,
  ypdLink,
  ypdMidGrey,
  ypdTextGrey,
  ypdWhite,
} from '../../utils/colors';
import styles from '../../assets/styles/ProgressActivity.scss';
import CalendarComponent from '../../components/CalendarComponent';
import { Shadow } from 'react-native-shadow-2';
import Heading from '../../components/Heading';
import { useSelector } from 'react-redux';
import { Fonts } from '../../utils';
import { ThemeContext } from '../../context/ThemeContext';
import ProgressActivityModal from '../../components/ProgressActivityModal';
import { ratingInfo } from '../../utils/utils';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Icon<PERSON>utton } from 'react-native-paper';
const { width: screenWidth } = Dimensions.get('window');

const ProgressActivity = ({ currentDate, setCurrentDate }) => {
  const navigation = useNavigation();
  const route = useRoute();
  const { fromLogDose = false, modality = null } = route.params || {};

  const initialDate = fromLogDose
    ? new Date().toISOString().split('T')[0]
    : currentDate || new Date().toISOString().split('T')[0];

  const [currentDateState, setCurrentDateState] = useState(initialDate);
  const effectiveCurrentDate = currentDate || currentDateState;
  const { theme } = useContext(ThemeContext);
  const isDarkMode = theme.dark;
  const [viewMode, setViewMode] = useState('week');
  const [modalVisible, setModalVisible] = useState(false);
  const [modalContent, setModalContent] = useState({
    strain: '',
    notes: '',
    dosageType: '',
    cannabisType: '',
    isCheckin: false,
  });
  const suggestions = useSelector(
    (state) => state.suggestions.suggestions || [],
  );
  const streakDates = useSelector(
    (state) => state.suggestions.streakDates || [],
  );

  const profileData = useSelector((state) => state.profile);
  const createdAt = profileData?.createdAt;

  let activeDates = [];
  if (fromLogDose && createdAt) {
    const startDate = new Date(createdAt).toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];
    const relevantSuggestions = suggestions.filter((suggestion) => {
      if (!suggestion?.checkinAt) return false;
      if (suggestion?.suggestionType === 'checkin') {
        const suggestionModality = JSON.parse(suggestion.checkinType)['M'][
          'modality'
        ]['S'];
        if (suggestionModality !== modality) {
          return false;
        }
      } else return false;
      const suggestionDate = new Date(suggestion.checkinAt)
        .toISOString()
        .split('T')[0];
      return suggestionDate >= startDate && suggestionDate <= today;
    });
    activeDates = [
      ...new Set(
        relevantSuggestions.map(
          (s) => new Date(s.checkinAt).toISOString().split('T')[0],
        ),
      ),
    ].sort();
  }

  const getActivityCards = (date) => {
    const dateSuggestions = suggestions.filter((suggestion) => {
      if (!suggestion?.checkinAt) return false;
      if (fromLogDose) {
        if (suggestion?.suggestionType === 'checkin') {
          const suggestionModality = JSON.parse(suggestion.checkinType)['M'][
            'modality'
          ]['S'];
          if (suggestionModality !== modality) {
            return false;
          }
        } else return false;
      }
      const suggestionDate = new Date(suggestion.checkinAt)
        .toISOString()
        .split('T')[0];
      return suggestionDate === date;
    });

    const activityCards = [];
    dateSuggestions.forEach((suggestion) => {
      const checkinTime = new Date(suggestion.checkinAt).toLocaleString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      let title = null;
      let firstRow = null;
      let secondRow = null;
      let thirdRow = null;
      let fourthRow = null;
      let fifthRow = null;
      let sixthRow = null;
      let strain = suggestion.strain || 'No data';
      let notes = suggestion.notes || 'No data';
      let dosageType = suggestion.dosageType || 'No data';
      let cannabisType = suggestion.cannabisType || 'No data';
      let overallEffect = suggestion.overallEffect;
      let overallSideEffect = suggestion.overallSideEffect;

      const getUnit = (value, isPuffModality) => {
        if (!isPuffModality) return 'mg';
        return value === '1' || value === 1 ? 'puff' : 'puffs';
      };

      if (suggestion.suggestionType === 'checkin') {
        const parsedCheckin = JSON.parse(suggestion.checkinType)['M'];
        const modality = parsedCheckin['modality']['S'];
        const doseSuggested = parsedCheckin['previousValue']['N'];
        const doseTaken = parsedCheckin['selectedDosage']['N'];

        const isPuffModality = modality === 'thci' || modality === 'cbdi';
        const modalityLabel =
          {
            thci: 'Inhaled THC',
            thco: 'Oral THC',
            cbdi: 'Inhaled CBD',
            cbdo: 'Oral CBD',
          }[modality] || 'Unknown Modality';

        title = 'Dose taken';
        firstRow = `${modalityLabel}`;
        secondRow = `Dose Suggested: ${doseSuggested} ${getUnit(doseSuggested, isPuffModality)}`;
        thirdRow = `Dose Taken: ${doseTaken} ${getUnit(doseTaken, isPuffModality)}`;
      } else {
        if (suggestion.suggestionType === 'journal') {
          title = 'How am I feeling';
        } else {
          title =
            suggestion.suggestionType === 'initial'
              ? 'Initial Suggestion'
              : 'Suggestion';
        }
        const thci = suggestion['thci'];
        const thco = suggestion['thco'];
        const cbdi = suggestion['cbdi'];
        const cbdo = suggestion['cbdo'];

        firstRow =
          ['both', 'thc'].includes(cannabisType) &&
          ['both', 'inhaled'].includes(dosageType)
            ? `Inhaled THC: ${thci} ${getUnit(thci, true)}`
            : null;
        secondRow =
          ['both', 'thc'].includes(cannabisType) &&
          ['both', 'oral'].includes(dosageType)
            ? `Oral THC: ${thco} ${getUnit(thco, false)}`
            : null;
        thirdRow =
          ['both', 'cbd'].includes(cannabisType) &&
          ['both', 'inhaled'].includes(dosageType)
            ? `Inhaled CBD: ${cbdi} ${getUnit(cbdi, true)}`
            : null;
        fourthRow =
          ['both', 'cbd'].includes(cannabisType) &&
          ['both', 'oral'].includes(dosageType)
            ? `Oral CBD: ${cbdo} ${getUnit(cbdo, false)}`
            : null;
        fifthRow =
          overallEffect !== null && overallEffect !== undefined
            ? `How I Felt: ${ratingInfo[overallEffect]?.label || 'No data'}`
            : null;
        sixthRow =
          overallSideEffect !== null && overallSideEffect !== undefined
            ? `Side Effects: ${ratingInfo[overallSideEffect]?.label || 'No data'}`
            : null;
      }

      activityCards.push({
        id: `${suggestion.id}-checkin`,
        topLeft: checkinTime,
        topRight: title,
        bottomLeft1: firstRow,
        bottomLeft2: secondRow,
        bottomLeft3: thirdRow,
        bottomLeft4: fourthRow,
        bottomLeft5: fifthRow,
        bottomLeft6: sixthRow,
        strain,
        notes,
        dosageType,
        cannabisType,
        isCheckin: suggestion.suggestionType === 'checkin',
      });
    });

    return activityCards;
  };

  const activityCards = !fromLogDose
    ? getActivityCards(effectiveCurrentDate)
    : [];
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString([], {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getShadowColor = (type) => {
    switch (type) {
      case 'Initial Suggestion':
        return stopColor1;
      case 'Suggestion':
        return ypdLink;
      case 'Dose taken':
        return ypdGreen;
      default:
        return ypdBlue;
    }
  };

  const handleCardPress = (card) => {
    setModalContent({
      strain: card.strain,
      dosageType: card.isCheckin ? '' : card.dosageType,
      cannabisType: card.isCheckin ? '' : card.cannabisType,
      isCheckin: card.isCheckin,
      notes: card.notes,
    });
    setModalVisible(true);
  };

  const renderActivityCard = (card) => (
    <TouchableOpacity
      key={card.id}
      onPress={() => handleCardPress(card)}
      style={styles.timelineRow}
    >
      <View style={styles.cardWithConnector}>
        <View style={styles.horizontalLine} />
        <Shadow
          distance={2}
          offset={[0, 3]}
          startColor="rgba(0, 0, 0, 0.05)"
          sides={{ bottom: true }}
          style={{ width: screenWidth - 80 }}
        >
          <View
            style={[
              styles.card,
              { backgroundColor: theme.colors.cardBackground },
            ]}
          >
            <View style={styles.cardContent}>
              <View style={styles.cardTopRow}>
                <Heading
                  text={card.topLeft}
                  fontFamily={Fonts.MEDIUM}
                  size="sm"
                  color={theme.colors.text}
                />
                <Heading
                  text={card.topRight}
                  fontSize={18}
                  color={
                    card.topRight === 'Initial Suggestion'
                      ? isDarkMode
                        ? ypdWhite
                        : theme.colors.lightText
                      : getShadowColor(card.topRight)
                  }
                />
              </View>
              <View style={styles.cardBottomRow}>
                <View>
                  {[1, 2, 3, 4, 5, 6].map((i) => {
                    const key = `bottomLeft${i}`;
                    return card[key] ? (
                      <View
                        key={key}
                        style={[
                          styles.cardTextRow,
                          key === 'bottomLeft5' && styles.howIFeltRow,
                        ]}
                      >
                        <Heading
                          text={card[key]}
                          size="xsmall"
                          color={theme.colors.text}
                        />
                      </View>
                    ) : null;
                  })}
                </View>
              </View>
            </View>
            <View
              style={[
                styles.cardRightBorder,
                { backgroundColor: getShadowColor(card.topRight) },
              ]}
            />
          </View>
        </Shadow>
      </View>
    </TouchableOpacity>
  );

  const activityContent = () => (
    <>
      {fromLogDose && (
        <SafeAreaView>
          <View style={styles.closeButton}>
            <IconButton
              icon="close"
              size={20}
              iconColor={theme.colors.text}
              style={styles.closeIcon}
              onPress={() => navigation.goBack()}
            />
          </View>
        </SafeAreaView>
      )}
      {fromLogDose ? (
        activeDates.length > 0 ? (
          <ScrollView showsVerticalScrollIndicator={false}>
            {activeDates.map((date) => {
              const dateActivityCards = getActivityCards(date);
              return (
                <View
                  key={date}
                  style={[
                    styles.timelineContainer,
                    { backgroundColor: theme.colors.lightTextGray },
                  ]}
                >
                  <View
                    style={[
                      styles.dateHeader,
                      { backgroundColor: theme.colors.lightTextGray },
                    ]}
                  >
                    <Heading
                      text={formatDate(date)}
                      style={[styles.dateText, { color: theme.colors.text }]}
                    />
                  </View>
                  <View
                    style={[
                      styles.timelineColumn,
                      { backgroundColor: theme.colors.lightTextGray },
                    ]}
                  >
                    <View style={styles.dot} />
                    <View style={styles.verticalLine} />
                    <View style={styles.dot} />
                  </View>
                  {dateActivityCards.map(renderActivityCard)}
                </View>
              );
            })}
          </ScrollView>
        ) : (
          <View style={styles.noActivitiesContainer}>
            <Heading
              text="No activities yet"
              size="md"
              textAlign="center"
              color={ypdTextGrey}
            />
          </View>
        )
      ) : activityCards.length > 0 ? (
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            style={[
              styles.timelineContainer,
              { backgroundColor: theme.colors.lightTextGray },
            ]}
          >
            <View
              style={[
                styles.dateHeader,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <Heading
                text={formatDate(effectiveCurrentDate)}
                style={[styles.dateText, { color: theme.colors.text }]}
              />
            </View>
            <View
              style={[
                styles.timelineColumn,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <View style={styles.dot} />
              <View style={styles.verticalLine} />
              <View style={styles.dot} />
            </View>
            {activityCards.map(renderActivityCard)}
          </View>
        </ScrollView>
      ) : (
        <View style={styles.noActivitiesContainer}>
          <Heading
            text="No activities for this date"
            size="md"
            textAlign="center"
            color={ypdTextGrey}
          />
        </View>
      )}
    </>
  );

  const renderCalendar = () => (
    <CalendarComponent
      initialDate={effectiveCurrentDate}
      streakDates={streakDates}
      onDateChange={setCurrentDate || setCurrentDateState}
      initialViewMode={viewMode}
      isLocked={false}
      onViewModeChange={setViewMode}
    />
  );

  return (
    <View
      style={[
        styles.calenderContainer,
        { backgroundColor: theme.colors.lightTextGray },
      ]}
    >
      <ProgressActivityModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        strain={modalContent.strain}
        dosageType={modalContent.dosageType}
        cannabisType={modalContent.cannabisType}
        isCheckin={modalContent.isCheckin}
        notes={modalContent.notes}
      />
      {fromLogDose ? (
        activityContent()
      ) : viewMode === 'month' ? (
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.weeklyCalendarView}>
            {isDarkMode ? (
              renderCalendar()
            ) : (
              <Shadow
                distance={10}
                startColor={ypdMidGrey}
                offset={[0, 0]}
                sides={{ top: true, bottom: true }}
              >
                {renderCalendar()}
              </Shadow>
            )}
          </View>
          {activityContent()}
        </ScrollView>
      ) : (
        <>
          <View style={styles.weeklyCalendarView}>
            {isDarkMode ? (
              renderCalendar()
            ) : (
              <Shadow
                distance={10}
                startColor={ypdMidGrey}
                offset={[0, 0]}
                sides={{ top: true, bottom: true }}
              >
                {renderCalendar()}
              </Shadow>
            )}
          </View>
          {activityContent()}
        </>
      )}
    </View>
  );
};

export default ProgressActivity;
