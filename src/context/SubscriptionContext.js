import React, { createContext, useContext, useState, useEffect } from 'react';
import * as RNIap from 'react-native-iap';
import { Platform } from 'react-native';

const SUBSCRIPTION_SKUS = Platform.select({
  ios: ['ypd_basic', 'ypd_standard', 'ypd_premium_month'],
  android: ['ypd_free'],
});

export const SubscriptionContext = createContext(null);

export const SubscriptionProvider = ({ children }) => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkSubscription = async () => {
      try {
        await RNIap.initConnection();
        const purchases = await RNIap.getAvailablePurchases();
        const activeSubscription = purchases.find(
          (purchase) =>
            SUBSCRIPTION_SKUS.includes(purchase.productId) &&
            (purchase.transactionReceipt ? true : false),
        );

        if (activeSubscription) {
          setSubscription(activeSubscription);
        } else {
          setSubscription({ productId: 'free' });
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
        setSubscription({ productId: 'free' });
      } finally {
        setLoading(false);
      }
    };

    checkSubscription();
  }, []);

  return (
    <SubscriptionContext.Provider value={{ subscription, loading }}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error(
      'useSubscription must be used within a SubscriptionProvider',
    );
  }
  return context;
};
