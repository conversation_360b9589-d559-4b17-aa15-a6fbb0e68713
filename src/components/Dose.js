import {
  View,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import CannabisDropDown from '../screens/profileBuilding/cannabisDropdown';
import {
  Fonts,
  cbdiData,
  cbdoData,
  thciData,
  thcoData,
  resetLoadingState,
} from '../utils';
import Heading from './Heading';
import Input from './Input';
import { ypdOldGreen, ypdTextGrey, ypdWhite } from '../utils/colors';
import ProfileBuilderButton from './ProfileBuilderButton';
import {
  setDosage,
  setCustomSideEffects,
  setSideEffects,
} from '../redux/slices/profileSlice';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import styles from '../assets/styles/ProfileBuilderStyles';
import { createDoseSuggestion } from '../api/suggestion';
import uuid from 'react-native-uuid';
import { useCallback, useContext, useState } from 'react';
import { resetCheckinState } from '../redux/slices/checkinSlice';
import { updateUserProfile } from '../api/profile';
import { recordEvent } from '../api/events';
import { ThemeContext } from '../context/ThemeContext';

const Dose = ({ isCheckin = false }) => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const profile = useSelector((state) => state.profile);
  const checkinData = useSelector((state) => state.checkIn);
  const dosage = useSelector((state) => state.profile.initialDose);
  const dosageType = useSelector((state) => state.profile.dosageType);
  const cannabisType = useSelector((state) => state.profile.cannabisType);

  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion,
  );
  const thciLastSuggestion = useSelector(
    (state) => state.suggestions.thciLastSuggestion,
  );
  const thcoLastSuggestion = useSelector(
    (state) => state.suggestions.thcoLastSuggestion,
  );
  const cbdiLastSuggestion = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion,
  );
  const cbdoLastSuggestion = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion,
  );

  const [thci, setTHCI] = useState(thciLastSuggestion?.thci || dosage?.thcVape);
  const [thco, setTHCO] = useState(thcoLastSuggestion?.thco || dosage?.thcOral);
  const [cbdi, setCBDI] = useState(cbdiLastSuggestion?.cbdi || dosage?.cbdVape);
  const [cbdo, setCBDO] = useState(cbdoLastSuggestion?.cbdo || dosage?.cbdOral);
  const [strain, setStrain] = useState(
    lastSuggestion?.strain || dosage?.strain,
  );
  const [dropdownsLocked, setDropdownsLocked] = useState(false);

  const thciEnabled =
    ['both', 'thc'].includes(cannabisType) &&
    ['both', 'inhaled'].includes(dosageType) &&
    !dropdownsLocked;

  const thcoEnabled =
    ['both', 'thc'].includes(cannabisType) &&
    ['both', 'oral'].includes(dosageType) &&
    !dropdownsLocked;

  const cbdiEnabled =
    ['both', 'cbd'].includes(cannabisType) &&
    ['both', 'inhaled'].includes(dosageType) &&
    !dropdownsLocked;

  const cbdoEnabled =
    ['both', 'cbd'].includes(cannabisType) &&
    ['both', 'oral'].includes(dosageType) &&
    !dropdownsLocked;

  const oralDosePlaceholder = '0 mg';
  const inhaledDosePlaceholder = '0 puff(s)';
  const hasDose = thci || thco || cbdi || cbdo;

  const [isLoading, setIsLoading] = useState(false);

  resetLoadingState(setIsLoading);

  const handleContinuePress = async () => {
    setDropdownsLocked(true);
    setIsLoading(true);
    Keyboard.dismiss();

    const suggestionData = {
      id: uuid.v4(),
      userId: profile.userId,
      username: profile.username,
      loopN: lastSuggestion?.loopN + 1 || 1,
      suggestionType: 'initial',
      dosageType: profile.dosageType,
      cannabisType: profile.cannabisType,
      strain: strain,
      rthci: thci,
      rthco: thco,
      rcbdi: cbdi,
      rcbdo: cbdo,
      notes: checkinData.notes,
      sideEffects: checkinData.sideEffects,
      overallEffect: checkinData.overallEffect,
      overallSideEffect: checkinData.overAllSideEffect,
      effects: JSON.stringify(checkinData.problemLevel),
    };

    const updatedSideEffects = [
      ...new Set([...profile.sideEffects, ...checkinData.sideEffects]),
    ];

    const updatedCustomSideEffects = [
      ...new Set([
        ...profile.customSideEffects,
        ...checkinData.customSideEffects,
      ]),
    ];

    const updatedProfile = {
      ...profile,
      sideEffects: updatedSideEffects,
      customSideEffects: updatedCustomSideEffects,
    };
    try {
      await updateUserProfile(updatedProfile);
    } catch (error) {
      console.error('Error updating profile:', error);
    }

    dispatch(setSideEffects(updatedSideEffects));
    dispatch(setCustomSideEffects(updatedCustomSideEffects));

    if (isCheckin) {
      await saveDosingEvent(isCheckin);
      navigation.navigate('SaveDose', { suggestionData: suggestionData });
    } else {
      dispatch(resetCheckinState());
      await createDoseSuggestion(suggestionData);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      await saveDosingEvent();
      const updatedDosage = {
        ...dosage,
        thcVape: thci,
        thcOral: thco,
        cbdVape: cbdi,
        cbdOral: cbdo,
        strain: strain,
      };
      dispatch(setDosage(updatedDosage));
      navigation.navigate('Step6', { profileBuilding: true });
    }
    setIsLoading(false);
  };

  const saveDosingEvent = async (isDosing = false) => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'lastDose',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      isDosing ? 'Dosing Event' : 'Profile Building Event',
      profile.username,
      true,
      isDosing ? '#D5' : '#PB7',
      attributes,
      createdAt,
    );
  };

  return (
    <>
      <View style={styles.dropDownContainer}>
        <CannabisDropDown
          disabled={!thciEnabled || isLoading}
          data={[
            ...thciData,
            ...(!thciData.some((item) => item.value === thci)
              ? [{ label: `${thci} puff(s)`, value: thci }]
              : []),
          ].sort((a, b) => a.value - b.value)}
          label="THC Vape"
          placeholder={inhaledDosePlaceholder}
          value={thci}
          onValueChange={(value) => {
            if (!isLoading) setTHCI(value);
          }}
        />
        <CannabisDropDown
          disabled={!thcoEnabled || isLoading}
          data={[
            ...thcoData,
            ...(!thcoData.some((item) => item.value === thco)
              ? [{ label: `${thco} mg`, value: thco }]
              : []),
          ].sort((a, b) => a.value - b.value)}
          label="THC Oral"
          placeholder={oralDosePlaceholder}
          value={thco}
          onValueChange={(value) => {
            if (!isLoading) setTHCO(value);
          }}
        />
        <CannabisDropDown
          disabled={!cbdiEnabled || isLoading}
          data={[
            ...cbdiData,
            ...(!cbdiData.some((item) => item.value === cbdi)
              ? [{ label: `${cbdi} puff(s)`, value: cbdi }]
              : []),
          ].sort((a, b) => a.value - b.value)}
          label="CBD Vape"
          placeholder={inhaledDosePlaceholder}
          value={cbdi}
          onValueChange={(value) => {
            if (!isLoading) setCBDI(value);
          }}
        />
        <CannabisDropDown
          disabled={!cbdoEnabled || isLoading}
          data={[
            ...cbdoData,
            ...(!cbdoData.some((item) => item.value === cbdo)
              ? [{ label: `${cbdo} mg`, value: cbdo }]
              : []),
          ].sort((a, b) => a.value - b.value)}
          label="CBD Oral"
          placeholder={oralDosePlaceholder}
          value={cbdo}
          onValueChange={(value) => {
            if (!isLoading) setCBDO(value);
          }}
        />
        <View style={styles.strainInput}>
          <Heading
            text="Strains (if applicable)"
            size="sm"
            fontFamily={Fonts.REGULAR}
            color={theme.colors.text}
          />
        </View>
        <TouchableWithoutFeedback disabled={!isLoading}>
          <KeyboardAvoidingView>
            <View pointerEvents={isLoading ? 'none' : 'auto'}>
              <Input
                value={strain}
                onChangeText={(value) => {
                  if (!isLoading) setStrain(value);
                }}
                placeholder="none"
                hasValue={!!strain}
                editable={!isLoading}
                disabled={isLoading}
                style={isLoading ? { opacity: 0.5 } : {}}
              />
            </View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </View>
      <View style={styles.bottomButton}>
        <ProfileBuilderButton
          activityIndicator={isLoading}
          continueText={hasDose ? 'Next' : 'Skip'}
          onContinuePress={handleContinuePress}
          continueDisabled={(isCheckin && !hasDose) || isLoading}
          continueButtonStyle={{
            backgroundColor: hasDose ? ypdOldGreen : ypdWhite,
            borderWidth: hasDose ? 0 : 1,
            borderColor: hasDose ? 'transparent' : ypdOldGreen,
          }}
          backDisabled={isLoading}
          onBackPress={() => navigation.goBack()}
          onlyNextButton={!isCheckin}
        />
      </View>
    </>
  );
};

export default Dose;
