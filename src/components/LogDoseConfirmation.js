import { SafeAreaView, ScrollView, View, Modal } from 'react-native';
import { useContext } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../context/ThemeContext';
import { ypdWhite } from '../utils/colors';
import Heading from './Heading';
import styles from '../assets/styles/LogDoseConfirmation.scss';
import { Fonts } from '../utils';
import CircularTileSlider from './CircularTileSlider';
import { useSelector } from 'react-redux';
import DoubleTick from '../assets/svgs/checkin/DoubleTick';
import Thunder from '../assets/svgs/checkin/Thunder';
import CustomButton from './CustomButton';

const LogDoseConfirmation = ({}) => {
  const route = useRoute();
  const { newCheckin, cannabisType, dosageType } = route.params;

  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const { checkinStats = {} } = useSelector((state) => state.suggestions || {});
  const { dailyCheckins, weeklyStreaks, fullStreakWeeks } = checkinStats;

  const doseTypeText =
    cannabisType && dosageType
      ? `${cannabisType.toUpperCase()} ${
          dosageType.charAt(0).toUpperCase() + dosageType.slice(1)
        }`
      : 'your';

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View style={styles.container}>
          <View style={styles.headingContainer}>
            <Heading
              text={
                newCheckin
                  ? 'Yay on your 1st check in!'
                  : `You're on a ${weeklyStreaks}-day streak!`
              }
              size="lg"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text={`You've successfully logged your ${doseTypeText} dose. Keep up the great work!`}
              size="md"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>
          <View>
            <CircularTileSlider
              streakCount={weeklyStreaks}
              title="Progress Achievement"
              checkInText={false}
            />
            <View style={[styles.dailyWeeklyContainer]}>
              <View style={styles.dailyWeekly}>
                <DoubleTick />
                <Heading
                  text={dailyCheckins}
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <Heading
                  text="Daily check-ins"
                  size="small"
                  color={theme.colors.placeholderTextColor}
                />
              </View>

              <View style={styles.dailyWeekly}>
                <Thunder />
                <Heading
                  text={fullStreakWeeks}
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <Heading
                  text="Weekly streaks"
                  size="small"
                  color={theme.colors.placeholderTextColor}
                />
              </View>
            </View>
            <View style={styles.headingContainer}>
              <Heading
                text={'Use your judgment.'}
                textAlign="center"
                size="sm"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.text}
              />

              <Heading
                text={
                  'Oral cannabis may take up to 2 hours to take effect; inhaled cannabis, up to 1 hour. If you Experience any side effects, call 911 or go to the ER. '
                }
                size="xsm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.buttonContainer}>
              <CustomButton
                variant={theme.dark ? 'letUsKnow' : 'purple'}
                title="Take me home"
                color={ypdWhite}
                onPress={() =>
                  navigation.reset({
                    index: 0,
                    routes: [
                      {
                        name: 'Tabs',
                        state: {
                          index: 0,
                          routes: [
                            {
                              name: 'Home',
                            },
                          ],
                        },
                      },
                    ],
                  })
                }
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LogDoseConfirmation;
