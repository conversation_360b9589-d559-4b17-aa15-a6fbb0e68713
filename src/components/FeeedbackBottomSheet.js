import { useState, useRef, useContext, useEffect } from 'react';
import {
  Animated,
  View,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { IconButton } from 'react-native-paper';
import Heading from './Heading';
import Input from './Input';
import CustomButton from './CustomButton';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';
import { useSelector } from 'react-redux';
import { ypdGreen, ypdRed, ypdTeal } from '../utils/colors';
import styles from '../assets/styles/FeedbackBottomSheet.scss';
import { createNewFeedback } from '../api/feedback';
import { getDevicePlatform } from '../utils/utils';
import { requestFeedback } from '../utils/feedback';

const FeedbackBottomSheet = () => {
  const { theme } = useContext(ThemeContext);
  const scrollViewRef = useRef();

  const profileData = useSelector((state) => state.profile);

  const [email, setEmail] = useState(profileData.email);
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(true);
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);

  useEffect(() => {
    const checkFeedbackModalVisibility = async () => {
      const canRequestFeedback = await requestFeedback(profileData.userId);
      if (canRequestFeedback) {
        showBottomSheet();
      }
    };
    checkFeedbackModalVisibility();
  }, []);

  const showBottomSheet = () => {
    setBottomSheetVisible(true);
    setEmail(profileData.email);
  };

  const hideBottomSheet = () => {
    setFeedback('');
    setLoading(false);
    setErrorMessage('');
    setIsEmailValid(true);
    setBottomSheetVisible(false);
  };

  const checkEmailValidity = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const addFeedback = async () => {
    setLoading(true);

    const params = {
      email: email,
      feedback: feedback,
      userId: profileData.userId,
      username: profileData.username,
    };

    try {
      await createNewFeedback(params);
      setFeedback('');
      setErrorMessage('');
      setSuccessMessage('Thank you for your feedback!');
      setTimeout(() => {
        hideBottomSheet();
        setSuccessMessage('');
      }, 2000);
    } catch (error) {
      console.error('Error adding user feedback:', error);
      setErrorMessage('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (email.trim() === '') {
      setErrorMessage('Email cannot be empty.');
      return;
    }

    if (feedback.trim() === '') {
      setErrorMessage('Feedback cannot be empty.');
      return;
    }

    if (!checkEmailValidity(email)) {
      setErrorMessage('Please enter a valid email address.');
      return;
    }

    await addFeedback();
  };

  return (
    <View style={styles.container}>
      {!isBottomSheetVisible && (
        <View
          style={[
            styles.floatingButton,
            {
              backgroundColor: ypdTeal,
              borderColor: theme.dark ? ypdGreen : 'transparent',
              borderWidth: theme.dark ? 1 : 0,
            },
          ]}
        >
          <IconButton
            icon="message-text-outline"
            size={24}
            iconColor={ypdGreen}
            onPress={showBottomSheet}
          />
        </View>
      )}
      <Modal
        transparent={true}
        visible={isBottomSheetVisible}
        animationType="none"
        onRequestClose={hideBottomSheet}
      >
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={[
                styles.bottomSheet,
                { backgroundColor: theme.colors.background },
              ]}
            >
              <IconButton
                icon="close"
                size={24}
                iconColor={theme.colors.text}
                onPress={hideBottomSheet}
                style={styles.crossIcon}
              />
              <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <ScrollView
                  ref={scrollViewRef}
                  contentContainerStyle={styles.contentContainerStyle}
                  keyboardShouldPersistTaps="handled"
                >
                  <Animated.View
                    style={[
                      styles.bottomSheet,
                      { backgroundColor: theme.colors.background },
                    ]}
                  >
                    <Heading
                      text="We value your feedback"
                      size="md"
                      textAlign="center"
                      fontFamily={Fonts.MEDIUM}
                      color={theme.colors.text}
                    />
                    {errorMessage && (
                      <Heading
                        text={errorMessage}
                        size="sm"
                        color={ypdRed}
                        style={styles.errorMessage}
                      />
                    )}
                    {successMessage && (
                      <Heading
                        text={successMessage}
                        size="sm"
                        color={ypdGreen}
                        style={styles.successMessage}
                      />
                    )}

                    <View
                      style={[
                        styles.infoContainer,
                        { backgroundColor: theme.colors.background },
                      ]}
                    >
                      <View style={styles.infoRow}>
                        <Heading
                          text="Release:"
                          size="sm"
                          fontFamily={Fonts.MEDIUM}
                          color={theme.colors.text}
                        />
                        <Heading
                          text={process.env.RELEASE}
                          size="sm"
                          fontFamily={Fonts.REGULAR}
                          color={theme.colors.text}
                        />
                      </View>
                      <View style={styles.infoRow}>
                        <Heading
                          text="Platform:"
                          size="sm"
                          fontFamily={Fonts.MEDIUM}
                          color={theme.colors.text}
                        />
                        <Heading
                          text={getDevicePlatform()}
                          size="sm"
                          fontFamily={Fonts.REGULAR}
                          color={theme.colors.text}
                        />
                      </View>
                    </View>
                    <View style={styles.emailContainer}>
                      <Heading
                        text="Email:"
                        size="sm"
                        fontFamily={Fonts.MEDIUM}
                        color={theme.colors.text}
                      />
                      <TextInput
                        style={[
                          styles.emailInput,
                          isEmailFocused && styles.emailInputFocused,
                          !isEmailValid && styles.invalidEmail,
                          { color: theme.colors.text },
                          { backgroundColor: theme.colors.background },
                        ]}
                        value={email}
                        onFocus={() => setIsEmailFocused(true)}
                        onBlur={() => {
                          setIsEmailValid(checkEmailValidity(email));
                          setIsEmailFocused(false);
                        }}
                        onChangeText={(text) => {
                          const trimmedText = text.trim();
                          setEmail(trimmedText);
                          const isValidEmail = checkEmailValidity(trimmedText);
                          setIsEmailValid(isValidEmail);
                          if (isValidEmail) {
                            setErrorMessage('');
                          } else {
                            setErrorMessage(
                              'Please enter a valid email address.',
                            );
                          }
                        }}
                        placeholder="Enter your email"
                        keyboardType="email-address"
                        autoCapitalize="none"
                      />
                    </View>

                    <Input
                      value={feedback}
                      onChangeText={(text) => setFeedback(text)}
                      placeholder="Enter feedback here..."
                      multiline={true}
                      textAlignVertical="top"
                      height={100}
                      borderRadius={10}
                      hasValue={!!feedback}
                      style={styles.textArea}
                    />

                    <View style={styles.buttonRow}>
                      <CustomButton
                        title="Submit"
                        onPress={(e) => handleSubmit(e)}
                        variant="green"
                        width="100%"
                        activityIndicator={loading}
                        disabled={
                          !isEmailValid || feedback.trim() === '' || loading
                        }
                      />
                    </View>
                  </Animated.View>
                </ScrollView>
              </TouchableWithoutFeedback>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </View>
      </Modal>
    </View>
  );
};

export default FeedbackBottomSheet;
