import { useContext, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  Animated,
  Easing,
  PanResponder,
} from 'react-native';
import { Button, IconButton } from 'react-native-paper';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  ypdOldGreen,
  ypdWhite,
  ypdTextGrey,
  ypdTeal,
  ypdGreen,
  ypdDarkModalGray,
  ypdYellow,
  ypdTextLightGrey,
  ypdBlack,
} from '../utils/colors';
import styles from '../assets/styles/SuggestionPopup';
import { ThemeContext } from '../context/ThemeContext';
import {
  setSelectedTheme,
  setSystemDefault,
} from '../redux/slices/appearanceSlice';

import DoseCheckin from '../assets/svgs/home/<USER>';
import DarkModeImage from '../assets/svgs/myProfile/DarkModeImage';
import Sun from '../assets/svgs/myProfile/Sun';
import Moon from '../assets/svgs/myProfile/Moon';
import { Fonts } from '../utils';
import Heading from './Heading';

const BulletPoint = ({ text, theme }) => (
  <View style={styles.bulletPoints}>
    <Text style={[styles.bulletPoint, { color: theme.colors.text }]}>•</Text>
    <Text style={[styles.bulletPointText, { color: theme.colors.text }]}>
      {text}
    </Text>
  </View>
);

const TAP_SLOP = 10;
const KNOB_TRAVEL = 40;

const SuggestionPopup = ({
  visible,
  onClose,
  data = [],
  isClose,
  cannabisType,
  dosageType,
  darkModeModal = false,
}) => {
  const { theme, setTheme } = useContext(ThemeContext);
  const dispatch = useDispatch();

  const slideAnim = useRef(new Animated.Value(theme.dark ? 1 : 0)).current;
  const toggleWidthRef = useRef(80);
  const startValRef = useRef(0);
  const knobBackground = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [ypdWhite, ypdBlack],
  });

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: theme.dark ? 1 : 0,
        duration: 0,
        useNativeDriver: false,
      }).start();
    }
  }, [visible, theme.dark]);

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: theme.dark ? 1 : 0,
        duration: 0,
        useNativeDriver: false,
      }).start();
    }
  }, [visible, theme.dark]);

  const handleThemeSelection = async (themeType) => {
    const isDark = themeType === 'dark';

    slideAnim.stopAnimation(() => {
      Animated.timing(slideAnim, {
        toValue: isDark ? 1 : 0,
        duration: 200,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    setTheme(themeType);
    dispatch(setSelectedTheme(themeType));
    if (themeType === 'system') {
      dispatch(setSystemDefault(true));
      await AsyncStorage.removeItem('user-theme');
    } else {
      dispatch(setSystemDefault(false));
      await AsyncStorage.setItem('user-theme', themeType);
    }
  };
  const PADDING = 2;

  const sliderTranslate = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [PADDING, KNOB_TRAVEL - PADDING],
  });

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, g) =>
        Math.abs(g.dx) > 2 || Math.abs(g.dy) > 2,
      onPanResponderTerminationRequest: () => false,

      onPanResponderGrant: () => {
        slideAnim.stopAnimation((val) => {
          startValRef.current = val;
        });
      },

      onPanResponderMove: (_, gestureState) => {
        let val = startValRef.current + gestureState.dx / KNOB_TRAVEL;
        val = Math.max(0, Math.min(1, val));
        slideAnim.setValue(val);
      },

      onPanResponderRelease: (evt, gestureState) => {
        const { dx, dy } = gestureState;

        const localX = evt.nativeEvent.locationX;
        const width = toggleWidthRef.current;

        const isTap = Math.abs(dx) <= TAP_SLOP && Math.abs(dy) <= TAP_SLOP;

        if (isTap) {
          if (localX < width / 2) {
            handleThemeSelection('light');
          } else {
            handleThemeSelection('dark');
          }
          return;
        }

        slideAnim.stopAnimation((val) => {
          if (val >= 0.5) {
            handleThemeSelection('dark');
          } else {
            handleThemeSelection('light');
          }
        });
      },
    }),
  ).current;

  const isCheckinPopup = !!cannabisType && !!dosageType;
  const doseTypeText =
    cannabisType && dosageType
      ? `${cannabisType.toUpperCase()} ${
          dosageType.charAt(0).toUpperCase() + dosageType.slice(1)
        }`
      : 'your dose';

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackground}>
        <View
          style={[
            styles.modalContainer,
            {
              paddingVertical: isCheckinPopup || darkModeModal ? 30 : 20,
              paddingHorizontal: isCheckinPopup || darkModeModal ? 30 : 20,
              width: darkModeModal ? '80%' : '90%',
              backgroundColor: darkModeModal
                ? theme.dark
                  ? ypdDarkModalGray
                  : ypdWhite
                : theme.colors.background,
            },
          ]}
        >
          {darkModeModal && (
            <View style={styles.closeButton}>
              <IconButton
                icon="close"
                size={25}
                iconColor={theme.colors.text}
                onPress={onClose}
              />
            </View>
          )}

          {darkModeModal ? (
            <>
              {theme.dark ? (
                <DarkModeImage style={styles.darkModeImage} />
              ) : (
                <DarkModeImage style={styles.lightModeImage} />
              )}

              <Text style={[styles.judgmentText, { color: theme.colors.text }]}>
                You can now use YPD in dark mode, easier on the eyes at night
                and better for the battery
              </Text>
              <Text style={[styles.judgmentText, { color: theme.colors.text }]}>
                To switch themes, use the toggle below:
              </Text>

              <View style={styles.themeToggleContainer}>
                <View
                  {...panResponder.panHandlers}
                  onLayout={(e) => {
                    toggleWidthRef.current = e.nativeEvent.layout.width || 80;
                  }}
                  style={[
                    styles.toggleButtonContainer,
                    {
                      width: 80,
                      height: 40,
                      borderRadius: 20,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      paddingHorizontal: 8,
                      overflow: 'hidden',
                      backgroundColor: theme.dark ? ypdTeal : ypdTextLightGrey,
                    },
                  ]}
                >
                  <Animated.View
                    pointerEvents="none"
                    style={{
                      position: 'absolute',
                      width: 36,
                      height: 36,
                      borderRadius: 18,
                      backgroundColor: knobBackground,
                      transform: [{ translateX: sliderTranslate }],
                    }}
                  />

                  <View
                    style={{ flex: 1, alignItems: 'center' }}
                    pointerEvents="none"
                  >
                    <Sun
                      color={!theme.dark ? ypdYellow : ypdTextGrey}
                      width={22}
                      height={22}
                      style={{ marginRight: 7 }}
                    />
                  </View>
                  <View
                    style={{ flex: 1, alignItems: 'center' }}
                    pointerEvents="none"
                  >
                    <Moon
                      color={theme.dark ? ypdGreen : ypdTextGrey}
                      width={22}
                      height={22}
                    />
                  </View>
                </View>
              </View>
            </>
          ) : isCheckinPopup ? (
            <>
              <DoseCheckin style={styles.doseCheckin} />
              <Text
                style={[
                  styles.titleText,
                  { color: theme.colors.text, fontFamily: Fonts.MEDIUM },
                ]}
              >
                Great! Your daily check-in {'\n'} for {doseTypeText} has been
                {'\n'}
                successfully logged.
              </Text>
              <View style={styles.divider} />
              <Heading
                text="Use your judgment."
                size="sm"
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
                style={{
                  marginBottom: '8',
                }}
              />
              <Text
                style={[
                  styles.infoText,
                  { color: theme.colors.text, fontFamily: Fonts.REGULAR },
                ]}
              >
                Oral cannabis may take up to 2 hours to take effect; inhaled
                cannabis, up to 1 hour.If you Experience any side effects, call
                911 or go to the ER.
              </Text>
              <View style={styles.buttonContainer}>
                <Button
                  style={styles.button}
                  mode="outlined"
                  buttonColor={ypdOldGreen}
                  textColor={theme.colors.text}
                  onPress={onClose}
                >
                  {isClose ? 'Close' : `I understand let's go!`}
                </Button>
              </View>
            </>
          ) : (
            <>
              {data.map((item, index) => (
                <BulletPoint key={index} text={item} theme={theme} />
              ))}
              <View style={styles.buttonContainer}>
                <Button
                  style={styles.button}
                  mode="outlined"
                  buttonColor={ypdOldGreen}
                  textColor={ypdTeal}
                  onPress={onClose}
                >
                  {isClose ? 'Close' : ' Agree'}
                </Button>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default SuggestionPopup;
