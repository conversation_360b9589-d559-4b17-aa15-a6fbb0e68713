import { useContext, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import { Fonts } from '../utils';
import ModalityCard from './ModalityCard';
import { ypdBlack, ypdTeal } from '../utils/colors';
import CustomButton from './CustomButton';
import styles from '../assets/styles/DoseSuggestionCard';
import Heading from './Heading';

const DoseSuggestionCard = ({ lastCheckinMap = {} }) => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const dosage = useSelector((state) => state.profile.initialDose);
  const dosageType = useSelector((state) => state.profile.dosageType);
  const cannabisType = useSelector((state) => state.profile.cannabisType);

  const [thciEnabled, setThciEnabled] = useState(true);
  const [thcoEnabled, setThcoEnabled] = useState(true);
  const [cbdiEnabled, setCbdiEnabled] = useState(true);
  const [cbdoEnabled, setCbdoEnabled] = useState(true);

  const [thciRemaining, setThciRemaining] = useState(0);
  const [thcoRemaining, setThcoRemaining] = useState(0);
  const [cbdiRemaining, setCbdiRemaining] = useState(0);
  const [cbdoRemaining, setCbdoRemaining] = useState(0);

  const [expandedBox, setExpandedBox] = useState(null);

  useEffect(() => {
    setThciEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setThcoEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
    setCbdiEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setCbdoEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
  }, [cannabisType, dosageType]);

  const thciLastSuggestion = useSelector(
    (state) => state.suggestions.thciLastSuggestion,
  );
  const thcoLastSuggestion = useSelector(
    (state) => state.suggestions.thcoLastSuggestion,
  );
  const cbdiLastSuggestion = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion,
  );
  const cbdoLastSuggestion = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion,
  );

  const addOption = () => {
    if (dosageType !== 'both') {
      navigation.navigate('Step7', {
        profileBuilding: false,
        marginTop: 50,
      });
    } else {
      navigation.navigate('Step6', {
        profileBuilding: false,
        marginTop: 50,
      });
    }
  };

  const handleDosePress = (doseType, modality) => {
    navigation.navigate('LogDose', {
      modality,
      dosageType: doseType,
      cannabisType: modality.startsWith('thc') ? 'THC' : 'CBD',
      lastCheckinTime: lastCheckinMap[modality]?.toISOString?.(),
    });
  };

  const pluralize = (value, unit) => {
    if (unit === 'puffs') {
      return value === 1 ? 'puff' : 'puffs';
    }
    return unit;
  };

  const getDoseTitle = ({
    enabled,
    lastSuggestion,
    dosageValue,
    label,
    unit,
  }) => {
    if (!enabled) return 'Add this option';

    const baseValue = lastSuggestion ?? dosageValue ?? 0;
    const finalUnit = pluralize(baseValue, unit);

    return `${label} - ${baseValue} ${finalUnit}`;
  };

  const thcoTitle = getDoseTitle({
    enabled: thcoEnabled,
    lastSuggestion: thcoLastSuggestion?.thco,
    dosageValue: dosage?.thcOral,
    label: 'THC Oral',
    unit: 'mg',
  });

  const thciTitle = getDoseTitle({
    enabled: thciEnabled,
    lastSuggestion: thciLastSuggestion?.thci,
    dosageValue: dosage?.thcVape,
    label: 'THC Inhaled',
    unit: 'puffs',
  });

  const cbdoTitle = getDoseTitle({
    enabled: cbdoEnabled,
    lastSuggestion: cbdoLastSuggestion?.cbdo,
    dosageValue: dosage?.cbdOral,
    label: 'CBD Oral',
    unit: 'mg',
  });

  const cbdiTitle = getDoseTitle({
    enabled: cbdiEnabled,
    lastSuggestion: cbdiLastSuggestion?.cbdi,
    dosageValue: dosage?.cbdVape,
    label: 'CBD Inhaled',
    unit: 'puffs',
  });

  const MODALITY_CONFIG = {
    thco: {
      enabled: thcoEnabled,
      remaining: thcoRemaining,
      title: thcoTitle,
      dosageType: 'oral',
      lockoutHours: 2,
      notificationId: 3,
      lastSuggestionTime: lastCheckinMap['thco'],
    },
    thci: {
      enabled: thciEnabled,
      remaining: thciRemaining,
      title: thciTitle,
      dosageType: 'inhaled',
      lockoutHours: 1,
      notificationId: 4,
      lastSuggestionTime: lastCheckinMap['thci'],
    },
    cbdo: {
      enabled: cbdoEnabled,
      remaining: cbdoRemaining,
      title: cbdoTitle,
      dosageType: 'oral',
      lockoutHours: 2,
      notificationId: 5,
      lastSuggestionTime: lastCheckinMap['cbdo'],
    },
    cbdi: {
      enabled: cbdiEnabled,
      remaining: cbdiRemaining,
      title: cbdiTitle,
      dosageType: 'inhaled',
      lockoutHours: 1,
      notificationId: 6,
      lastSuggestionTime: lastCheckinMap['cbdi'],
    },
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();

      const calcRemaining = (lastSuggestion, lockoutHours) => {
        if (!lastSuggestion) {
          return 0;
        }

        const suggestionTime = new Date(lastSuggestion);
        if (isNaN(suggestionTime.getTime())) {
          return 0;
        }
        const elapsedMs = now - suggestionTime;
        const lockoutMs = lockoutHours * 60 * 60 * 1000;
        const remainingMs = lockoutMs - elapsedMs;
        return Math.max(0, Math.floor(remainingMs / 1000));
      };

      setThciRemaining(
        thciEnabled
          ? calcRemaining(
              MODALITY_CONFIG.thci.lastSuggestionTime,
              MODALITY_CONFIG.thci.lockoutHours,
            )
          : 0,
      );
      setThcoRemaining(
        thcoEnabled
          ? calcRemaining(
              MODALITY_CONFIG.thco.lastSuggestionTime,
              MODALITY_CONFIG.thco.lockoutHours,
            )
          : 0,
      );
      setCbdiRemaining(
        cbdiEnabled
          ? calcRemaining(
              MODALITY_CONFIG.cbdi.lastSuggestionTime,
              MODALITY_CONFIG.cbdi.lockoutHours,
            )
          : 0,
      );
      setCbdoRemaining(
        cbdoEnabled
          ? calcRemaining(
              MODALITY_CONFIG.cbdo.lastSuggestionTime,
              MODALITY_CONFIG.cbdo.lockoutHours,
            )
          : 0,
      );
    }, 1000);

    return () => clearInterval(interval);
  }, [
    thciLastSuggestion,
    thcoLastSuggestion,
    cbdiLastSuggestion,
    cbdoLastSuggestion,
  ]);

  const renderExpandedBox = (modality, dosageType) => (
    <>
      <View>
        <View style={styles.expandedBox}>
          <Heading
            text={
              dosageType === 'oral'
                ? 'By mouth \nEvery 2 hours as needed'
                : 'By vape \nEvery 1 hour as needed'
            }
            fontSize={12}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.modalityCardExpandedText}
            style={[styles.expandedText]}
          />
          <CustomButton
            title="Log as Taken"
            variant="newGreen"
            width={130}
            height={40}
            borderRadius={25}
            fontFamily={Fonts.MEDIUM}
            textColor={ypdTeal}
            ioniconName="arrow-forward"
            iconColor={ypdTeal}
            flexDirection="row-reverse"
            fontSize={12}
            onPress={() => handleDosePress(dosageType, modality)}
          />
        </View>
      </View>
      <View style={styles.divider} />
    </>
  );

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.buttonContainer}>
        {Object.entries(MODALITY_CONFIG).map(([key, config], index, arr) => {
          const isExpanded = expandedBox === key;
          const {
            enabled,
            remaining,
            title,
            dosageType,
            lastSuggestionTime,
            lockoutHours,
            notificationId,
          } = config;

          return (
            <View
              key={key}
              style={[
                {
                  backgroundColor: isExpanded
                    ? theme.colors.modalityCards
                    : theme.colors.background,
                },
              ]}
            >
              <ModalityCard
                title={title}
                modality={key}
                timeRemaining={remaining}
                lockoutHours={lockoutHours}
                notificationId={notificationId}
                lastSuggestionTime={lastSuggestionTime}
                onPress={() =>
                  enabled && remaining === 0
                    ? setExpandedBox(isExpanded ? null : key)
                    : addOption()
                }
                iconName={
                  enabled && remaining === 0
                    ? isExpanded
                      ? null
                      : 'add'
                    : null
                }
                textColor={
                  isExpanded && !theme.dark ? ypdBlack : theme.colors.text
                }
                fontSize={15}
                fontFamily={Fonts.REGULAR}
                enabled={enabled && remaining > 0}
                style={{ backgroundColor: 'transparent' }}
                showDivider={index !== arr.length - 1 && !isExpanded}
              />
              {isExpanded && renderExpandedBox(key, dosageType)}
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default DoseSuggestionCard;
