import { useContext } from 'react';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Heading from './Heading';
import CustomButton from './CustomButton';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';

const ProfileListRender = ({
  text,
  textColor,
  listOfButtons,
  style,
  padding = [0, 0, 0, 0],
}) => {
  const { theme } = useContext(ThemeContext);
  const [pt, pr, pb, pl] = padding;

  return (
    <>
      {text ? (
        <Heading
          text={text}
          fontFamily={Fonts.MEDIUM}
          fontSize={20}
          color={textColor || theme.colors.lightText}
        />
      ) : null}

      {listOfButtons.map((btn, index) => (
        <CustomButton
          key={index}
          title={btn.title}
          onPress={btn.onPress}
          variant="transparent"
          height={35}
          width="100%"
          fontSize={18}
          color={textColor || theme.colors.lightText}
          justifyContent="space-between"
          flexDirection={btn.showIcon ? 'row-reverse' : 'row'}
          svgIcon={
            btn.showIcon ? (
              <MaterialIcons
                name="chevron-right"
                size={24}
                color={theme.colors.text}
              />
            ) : null
          }
          fontFamily="btn.fontFamily"
          activityIndicator={btn.activityIndicator}
          style={[
            style,
            {
              paddingTop: pt,
              paddingRight: pr,
              paddingBottom: pb,
              paddingLeft: pl,
            },
          ]}
        />
      ))}
    </>
  );
};

export default ProfileListRender;
