import { useEffect, useRef } from 'react';
import { Animated, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useSelector, useDispatch } from 'react-redux';
import {
  strokeIconColor,
  ypdGrayLightShade,
  ypdGrayShade,
  ypdMintGreen,
  ypdWhite,
} from '../utils/colors';
import {
  setCBDIReminder,
  setCBDOReminder,
  setTHCIReminder,
  setTHCOReminder,
} from '../redux/slices/notificationSlice';
import styles from '../assets/styles/ReminderToggle';
import LocalNotificationService from '../utils/localNotifactionService';

const MODALITY_ACTIONS = {
  cbdi: { action: setCBDIReminder, key: 'cbdiReminder' },
  cbdo: { action: setCBDOReminder, key: 'cbdoReminder' },
  thci: { action: setTHCIReminder, key: 'thciReminder' },
  thco: { action: setTHCOReminder, key: 'thcoReminder' },
};

const ReminderToggle = ({
  modality,
  enabled,
  notificationId,
  lockoutHours,
  lastSuggestionTime,
  theme,
}) => {
  const dispatch = useDispatch();
  const animX = useRef(new Animated.Value(reminder ? 1 : 0)).current;

  const { action, key } = MODALITY_ACTIONS[modality] || {};

  const reminder = useSelector((state) => state.notifications[key]);
  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );

  useEffect(() => {
    LocalNotificationService.cancelNotification(notificationId);
    if (showNotifications) {
      if (reminder && enabled) {
        LocalNotificationService.requestPermission(true);
        LocalNotificationService.scheduleLockoutNotification(
          notificationId,
          modality,
          lastSuggestionTime,
          lockoutHours,
        );
      }
    }
  }, [enabled, reminder, showNotifications]);

  useEffect(() => {
    Animated.timing(animX, {
      toValue: reminder ? 1 : 0,
      duration: 250,
      useNativeDriver: false,
    }).start();
  }, [reminder]);

  const handlePress = () => {
    if (action) dispatch(action(!reminder));
  };

  if (!showNotifications || !enabled) return null;

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.remindMeToggleBackground },
      ]}
    >
      <Text style={[styles.label, { color: theme.colors.text }]}>
        Remind me
      </Text>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <View
          style={[
            styles.toggle,
            { backgroundColor: reminder ? ypdMintGreen : strokeIconColor },
          ]}
        >
          <Text
            style={[
              styles.toggleText,
              {
                color: reminder ? ypdWhite : ypdGrayShade,
                left: reminder ? 6 : 'auto',
                right: reminder ? 'auto' : 6,
              },
            ]}
          >
            {reminder ? 'ON' : 'OFF'}
          </Text>

          <Animated.View
            style={[
              styles.knob,
              { backgroundColor: ypdWhite, borderRadius: 20 },
              {
                transform: [
                  { translateY: -13 },
                  { translateX: reminder ? 37 : 0 },
                ],
              },
            ]}
          >
            <Icon
              name={reminder ? 'notifications-active' : 'notifications-none'}
              size={18}
              color={reminder ? ypdMintGreen : ypdGrayLightShade}
            />
          </Animated.View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default ReminderToggle;
