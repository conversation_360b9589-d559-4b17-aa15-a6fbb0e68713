import { useCallback, useContext, useEffect, useState } from 'react';
import {
  View,
  SafeAreaView,
  ScrollView,
  InteractionManager,
} from 'react-native';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { Fonts } from '../utils';
import { getDoseConfig } from '../utils';
import { createDoseSuggestion } from '../api/suggestion';
import uuid from 'react-native-uuid';
import { recordEvent } from '../api/events';
import { ThemeContext } from '../context/ThemeContext';
import CustomButton from './CustomButton';
import Heading from './Heading';
import styles from '../assets/styles/LogDose.scss';
import Input from './Input';
import { ActivityIndicator, IconButton } from 'react-native-paper';
import BackButton from './BackButton';
import CannabisDropDown from '../screens/profileBuilding/cannabisDropdown';

const LogDose = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const route = useRoute();
  const { cannabisType, dosageType, modality, lastCheckinTime } =
    route.params || {};

  const [note, setNote] = useState('');
  const [screenTime, setScreenTime] = useState(null);
  const [loading, setLoading] = useState(false);
  const [logsLoading, setLogsLoading] = useState(false);
  const [dosePickerOpen, setDosePickerOpen] = useState(false);

  const profile = useSelector((state) => state.profile);

  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion,
  );

  const thciLastSuggestion = useSelector(
    (state) => state.suggestions.thciLastSuggestion,
  );
  const thcoLastSuggestion = useSelector(
    (state) => state.suggestions.thcoLastSuggestion,
  );
  const cbdiLastSuggestion = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion,
  );
  const cbdoLastSuggestion = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion,
  );

  const doseConfig = getDoseConfig(
    thciLastSuggestion.thci || profile.initialDose?.thcVape,
    thcoLastSuggestion.thco || profile.initialDose?.thcOral,
    cbdiLastSuggestion.cbdi || profile.initialDose?.cbdVape,
    cbdoLastSuggestion.cbdo || profile.initialDose?.cbdOral,
  );
  const normalizedCannabisType = cannabisType?.toUpperCase();
  const config = doseConfig[`${normalizedCannabisType} ${dosageType}`];
  const [selectedDosage, setSelectedDosage] = useState(config.lastDose || 0);

  const handleSelection = (value) => {
    setSelectedDosage(value);
  };

  const handleViewPastLogs = () => {
    setLogsLoading(true);
    InteractionManager.runAfterInteractions(() => {
      navigation.navigate('ProgressActivity', {
        fromLogDose: true,
        modality: modality,
      });
    });
  };
  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
      setLogsLoading(false);
    }, []),
  );

  useEffect(() => {
    const timeout = setTimeout(() => {}, 50);
    return () => clearTimeout(timeout);
  }, []);

  const saveCheckInEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'checkin',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Checkin Event',
      profile.username,
      true,
      '#CH1',
      attributes,
      createdAt,
    );
  };

  const handleConfirm = async () => {
    setLoading(true);

    const lastSuggestions = {
      thci: thciLastSuggestion?.thci,
      thco: thcoLastSuggestion?.thco,
      cbdi: cbdiLastSuggestion?.cbdi,
      cbdo: cbdoLastSuggestion?.cbdo,
    };

    const previousValue = lastSuggestions[modality];

    const suggestionData = {
      rthci: modality === 'thci' ? selectedDosage : lastSuggestions.thci,
      rthco: modality === 'thco' ? selectedDosage : lastSuggestions.thco,
      rcbdi: modality === 'cbdi' ? selectedDosage : lastSuggestions.cbdi,
      rcbdo: modality === 'cbdo' ? selectedDosage : lastSuggestions.cbdo,
      id: uuid.v4(),
      notes: note,
      userId: profile.userId,
      username: profile.username,
      suggestionType: 'checkin',
      checkinType: JSON.stringify({
        modality,
        previousValue,
        selectedDosage,
        sameValue: previousValue === selectedDosage,
      }),
      dosageType: profile.dosageType,
      cannabisType: profile.cannabisType,
      hitCBDMax: lastSuggestion?.hitCBDMax,
      hitTHCMax: lastSuggestion?.hitTHCMax,
      loopN: (lastSuggestion?.loopN ?? 0) + 1,
      isBELimitExceeded: lastSuggestion?.isBELimitExceeded,
    };

    try {
      await createDoseSuggestion(suggestionData);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      await saveCheckInEvent();
      navigation.navigate('LogDoseConfirmation', {
        newCheckin: !lastCheckinTime,
        cannabisType,
        dosageType,
      });
    } catch (e) {
      console.log(e);
    } finally {
      setNote('');
      setSelectedDosage(0);
    }
  };

  const formatTime = (time) => {
    if (!time) return '';
    const date = new Date(time);
    return date.toLocaleString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        <View style={styles.safeArea}>
          <View style={styles.closeButton}>
            <IconButton
              icon="close"
              iconColor={theme.colors.text}
              size={25}
              onPress={() => navigation.goBack()}
              disabled={loading || logsLoading}
            />
          </View>
          <Heading
            text="Current dose log"
            size="md"
            style={styles.mainHeading}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <View
            style={[
              styles.currentDoseSection,
              { backgroundColor: theme.colors.doseBG },
            ]}
          >
            <View style={styles.imageContainer}>{config.svg}</View>
            <View style={styles.textContainer}>
              <Heading
                text={config.heading}
                size="sm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.text}
              />
              <Heading
                text={`${selectedDosage || 0}${dosageType === 'oral' ? 'mg' : ' puff'}${selectedDosage !== 1 && dosageType !== 'oral' ? 's' : ''}`}
                size="sm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.modalityCardExpandedText}
              />
            </View>
            <Heading
              text="Edit"
              size="xs"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.link}
              disabled={loading || logsLoading}
              onPress={() =>
                !loading && !logsLoading && setDosePickerOpen(true)
              }
            />
            <CannabisDropDown
              logDose={true}
              data={[
                ...config.data,
                ...(!config.data.some((item) => item.value === selectedDosage)
                  ? [
                      {
                        label: `${selectedDosage || 0} ${dosageType === 'oral' ? 'mg' : 'puff'}${selectedDosage !== 1 && dosageType !== 'oral' ? 's' : ''}`,
                        value: selectedDosage || 0,
                      },
                    ]
                  : []),
              ].sort((a, b) => a.value - b.value)}
              label={config.heading}
              value={selectedDosage}
              onValueChange={handleSelection}
              dosePickerOpen={dosePickerOpen}
              setDosePickerOpen={setDosePickerOpen}
              disabled={loading || logsLoading}
            />
          </View>

          <View style={styles.pastDosesSection}>
            <Heading
              size="md"
              text="Time"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>
          <View
            style={[
              styles.doseSection,
              { backgroundColor: theme.colors.doseBG },
            ]}
          >
            <View
              style={[
                styles.imageContainer,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <IconButton
                icon="clock-outline"
                iconColor={theme.colors.text}
                size={25}
              />
            </View>
            <View style={styles.textContainer}>
              <Heading
                text="This dose"
                size="sm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.text}
              />
              <Heading
                text={formatTime(new Date())}
                size="xsmall"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.modalityCardExpandedText}
              />
            </View>
          </View>
          <View
            style={[
              styles.doseSection,
              { backgroundColor: theme.colors.doseBG },
            ]}
          >
            <View
              style={[
                styles.imageContainer,
                { backgroundColor: theme.colors.lightTextGray },
              ]}
            >
              <IconButton
                icon="clock-outline"
                iconColor={theme.colors.text}
                size={25}
              />
            </View>
            <View style={styles.textContainer}>
              <Heading
                text="Last taken"
                size="sm"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.text}
              />
              <Heading
                text={lastCheckinTime ? formatTime(lastCheckinTime) : 'No data'}
                size="xsmall"
                fontFamily={Fonts.REGULAR}
                color={theme.colors.modalityCardExpandedText}
              />
            </View>
            {lastCheckinTime &&
              (logsLoading ? (
                <ActivityIndicator size="small" color={theme.colors.link} />
              ) : (
                <Heading
                  text="View past logs"
                  size="xs"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.link}
                  disabled={loading || logsLoading}
                  onPress={handleViewPastLogs}
                />
              ))}
          </View>
          <View style={styles.pastDosesSection}>
            <Input
              value={note}
              onChangeText={setNote}
              placeholder="Include anything you'd like to note"
              style={styles.textArea}
              height={100}
              borderRadius={10}
              multiline={true}
              textAlignVertical="top"
              hasValue={!!note}
              disabled={loading || logsLoading}
            />
            <View style={styles.buttonSection}>
              <CustomButton
                title="Confirm dose taken"
                width="100%"
                height={50}
                variant="newGreen"
                onPress={handleConfirm}
                activityIndicator={loading}
                disabled={!selectedDosage || loading || logsLoading}
              />
            </View>

            <View style={styles.backButton}>
              <BackButton
                disabled={loading || logsLoading}
                onBackPress={() => navigation.navigate('Tabs')}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LogDose;
