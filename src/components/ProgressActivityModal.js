import React, { useContext } from 'react';
import { View, Modal, ScrollView } from 'react-native';
import Heading from './Heading';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';
import { IconButton } from 'react-native-paper';
import styles from '../assets/styles/ProgressActivityModal.scss';
import { ypdGreen, ypdGreen50, ypdTeal } from '../utils/colors';

const ProgressActivityModal = ({
  visible,
  onClose,
  strain,
  dosageType,
  cannabisType,
  isCheckin,
  notes,
}) => {
  const { theme } = useContext(ThemeContext);

  const renderText = (label, value) => {
    return (
      <View style={styles.textContainer}>
        <Heading
          text={`${label}: `}
          size="sm"
          color={theme.dark ? ypdGreen : ypdTeal}
          fontFamily={Fonts.MEDIUM}
        />
        <Heading
          text={value}
          size="sm"
          color={theme.colors.text}
          style={styles.modalValue}
        />
      </View>
    );
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View
          style={[
            styles.modalView,
            { backgroundColor: theme.colors.cardBackground },
          ]}
        >
          <IconButton
            icon="close"
            size={20}
            iconColor={theme.colors.text}
            style={styles.closeIcon}
            onPress={onClose}
          />

          <ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={true}
          >
            {isCheckin ? (
              renderText('Notes', notes)
            ) : (
              <View style={styles.dosageContainer}>
                {renderText('Strain', strain)}
                {renderText('Dosage Type', dosageType)}
                {renderText('Cannabis Type', cannabisType)}
                {renderText('Notes', notes)}
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default ProgressActivityModal;
