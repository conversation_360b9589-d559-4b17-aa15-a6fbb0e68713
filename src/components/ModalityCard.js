import { useContext } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { ThemeContext } from '../context/ThemeContext';
import {
  ypdBlack,
  ypdCicleStroke,
  ypdGreen,
  ypdMintGreen,
} from '../utils/colors';
import { Fonts } from '../utils';
import ReminderToggle from './ReminderToggle';

const ModalityCard = ({
  title,
  modality,
  lockoutHours,
  lastSuggestionTime,
  notificationId,
  timeRemaining,
  onPress,
  iconName,
  textColor,
  fontSize,
  fontFamily,
  enabled,
  style,
  iconStyle,
  showDivider = true,
}) => {
  const { theme } = useContext(ThemeContext);

  const formatTime = (totalSeconds) => {
    const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
    const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(
      2,
      '0',
    );
    const seconds = String(totalSeconds % 60).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  let mainTitle = title;
  let subtitle = null;

  if (timeRemaining) {
    const [h = '00', m = '00', s = '00'] = formatTime(timeRemaining).split(':');
    subtitle = `You can take your next dose in ${h}hr ${m}min ${s}sec`;
  }

  return (
    <TouchableOpacity
      style={[
        {
          paddingHorizontal: 0,
          paddingVertical: 12,
          borderRadius: 8,
          backgroundColor: 'white',
        },
        style,
      ]}
      onPress={() => onPress?.()}
      disabled={enabled}
    >
      <View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 10,
          }}
        >
          <Text
            style={{
              fontSize,
              fontFamily,
              color: textColor || theme.colors.text,
              flexShrink: 1,
            }}
          >
            {mainTitle}
          </Text>

          {iconName && (
            <Ionicons
              name={iconName}
              size={22}
              color={theme.dark ? ypdGreen : ypdBlack}
              style={[{ marginLeft: 8 }, iconStyle]}
            />
          )}
        </View>

        {subtitle && (
          <Text
            style={{
              marginTop: 4,
              fontSize: 12,
              fontFamily: Fonts.MEDIUM,
              color: ypdMintGreen,
              paddingLeft: 12,
            }}
          >
            {subtitle}
          </Text>
        )}
        <ReminderToggle
          theme={theme}
          enabled={enabled}
          modality={modality}
          lockoutHours={lockoutHours}
          notificationId={notificationId}
          lastSuggestionTime={lastSuggestionTime}
        />
      </View>
      {showDivider && timeRemaining === 0 && (
        <View
          style={{
            height: 1,
            backgroundColor: ypdCicleStroke,
            marginTop: 10,
            width: '100%',
          }}
        />
      )}
    </TouchableOpacity>
  );
};

export default ModalityCard;
