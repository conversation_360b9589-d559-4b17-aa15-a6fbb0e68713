import { useState, useEffect, useContext } from 'react';
import {
  Alert,
  AppState,
  BackHandler,
  Linking,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  View,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Geolocation from 'react-native-geolocation-service';
import styles from '../assets/styles/NotificationScreen.scss';
import Location from '../assets/svgs/myProfile/Location';
import CustomButton from '../components/CustomButton';
import WarningScreen from '../screens/warning/warningScreen';
import Heading from '../components/Heading';
import { ThemeContext } from '../context/ThemeContext';
import { recordEvent } from '../api/events';
import { Fonts } from '../utils';

const allowedCountries = process.env.ALLOWED_COUNTRIES;
const allowedRegions = JSON.parse(process.env.ALLOWED_REGIONS);
const MAX_RETRIES = 3;

export const Geofencing = ({ onStatusChange }) => {
  const { theme } = useContext(ThemeContext);

  const [errorCode, setErrorMessage] = useState(null);
  const [regionMessage, setRegionMessage] = useState('');
  const [permissionGranted, setPermissionGranted] = useState(null);
  const [isInAllowedRegion, setIsInAllowedRegion] = useState(null);
  const [showSettingsAlert, setShowSettingsAlert] = useState(false);
  const [isCheckingLocation, setIsCheckingLocation] = useState(true);

  const checkLocationPermission = async () => {
    console.log('Checking Location Permission');
    try {
      let granted;
      if (Platform.OS === 'android') {
        granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
      } else {
        granted =
          (await Geolocation.requestAuthorization('whenInUse')) === 'granted';
      }
      console.log('Permission:', granted);
      setPermissionGranted(granted);
      return granted;
    } catch (error) {
      console.log('Permission check error:', error, 'Error Code: YPDE001');
      await recordEventFailure(error.message, 'YPDE001');
      return false;
    }
  };

  const requestLocationPermission = async () => {
    console.log('Requesting Location Permission');
    try {
      let granted;
      if (Platform.OS === 'android') {
        granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Access Required',
            message: 'This app needs to access your location for working.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          setShowSettingsAlert(true);
          return granted;
        }
      } else {
        granted = await Geolocation.requestAuthorization('whenInUse');
      }
      console.log('Permission Access:', granted);

      if (
        granted === PermissionsAndroid.RESULTS.GRANTED ||
        granted === 'granted'
      ) {
        setPermissionGranted(true);
        return true;
      } else {
        setPermissionGranted(false);
        return false;
      }
    } catch (error) {
      console.log('Permission request error:', error, 'Error Code: YPDE002');
      await recordEventFailure(error.message, 'YPDE002');
      return false;
    }
  };

  const getCurrentLocation = async (retryCount) => {
    console.log('Checking location coordinates...');

    const getPosition = () => {
      return new Promise((resolve, reject) => {
        Geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
        );
      });
    };

    try {
      const position = await getPosition();
      const { latitude, longitude } = position.coords;
      const roundedLatitude = parseFloat(latitude.toFixed(3));
      const roundedLongitude = parseFloat(longitude.toFixed(3));

      const regionAllowed = await AsyncStorage.getItem('regionAllowed');
      const prevLatitude = parseFloat(await AsyncStorage.getItem('latitude'));
      const prevLongitude = parseFloat(await AsyncStorage.getItem('longitude'));

      console.log('Previous coordinates:', prevLatitude, prevLongitude);
      console.log('Current coordinates:', roundedLatitude, roundedLongitude);
      console.log('Region allowed (previous):', regionAllowed);

      if (
        prevLatitude !== roundedLatitude ||
        prevLongitude !== roundedLongitude ||
        regionAllowed !== 'true'
      ) {
        await AsyncStorage.setItem('latitude', roundedLatitude?.toString());
        await AsyncStorage.setItem('longitude', roundedLongitude?.toString());
        console.log('Coordinates changed');
        await checkLocationValidity(latitude, longitude, 1);
      } else {
        console.log('Coordinates not changed');
        if (regionAllowed === 'true') {
          onStatusChange?.('allowed');
          setIsInAllowedRegion(true);
        } else {
          setIsInAllowedRegion(false);
        }
      }
    } catch (error) {
      if (retryCount >= MAX_RETRIES) {
        setErrorMessage(
          'Failed to get location coordinates after several attempts. \n\nError code: YPDE003' +
            '\n\<NAME_EMAIL> and provide this error code.\n\n',
        );
        console.log(
          'Failed to get location coordinates after several attempts:',
          error,
          'Error Code: YPDE003',
        );
        await recordEventFailure(error.message, 'YPDE003');
      } else {
        console.log(`Retrying... (${retryCount + 1}/${MAX_RETRIES})`);
        await getCurrentLocation(retryCount + 1);
      }
    }
  };

  const checkLocationValidity = async (latitude, longitude, retryCount) => {
    console.log('Checking if in allowed regions...', new Date());

    try {
      const response = await fetch(
        `https://api.opencagedata.com/geocode/v1/json?q=${latitude}+${longitude}&key=${process.env.GEOCODING_API_KEY}`,
      );

      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.results.length > 0) {
        const {
          country,
          state,
          state_code: stateCode,
        } = data.results[0].components;
        console.log('Detected country:', country);
        console.log('Detected state:', state);
        console.log("Detected state's code:", stateCode);

        await AsyncStorage.setItem('country', country || 'unknown');
        await AsyncStorage.setItem('state', state || 'unknown');

        if (!country || !state) {
          throw new Error('Incomplete address components');
        }

        const isAllowedCountry = allowedCountries.includes(
          country.toLowerCase(),
        );
        const inAllowedRegion = allowedRegions.some(
          (region) =>
            region.state.toLowerCase() === state.toLowerCase() ||
            region.code === stateCode,
        );

        console.log('Is allowed country:', isAllowedCountry);
        console.log('Is in allowed region:', inAllowedRegion);

        const isAllowed = isAllowedCountry && inAllowedRegion;
        setIsInAllowedRegion(isAllowed);

        if (isAllowed) {
          onStatusChange?.('allowed');
          console.log('User is in the allowed region.');
          setRegionMessage('');
          await AsyncStorage.setItem('regionAllowed', 'true');
        } else {
          console.log('User is not in the allowed region.');
          const message = `Sorry, this app is not available in ${state}, ${country}.`;
          setRegionMessage(message);
          await AsyncStorage.setItem('regionAllowed', 'false');
        }
        await recordEventSuccess(data.results[0]);
      } else {
        throw new Error('No results from geocoding API');
      }
    } catch (error) {
      console.log(
        'Failed to check app availability!',
        `Attempt ${retryCount + 1} of ${MAX_RETRIES}`,
      );

      if (retryCount < MAX_RETRIES - 1) {
        await checkLocationValidity(latitude, longitude, retryCount + 1);
      } else {
        setErrorMessage(
          "Failed to check app's availability in your region after several attempts. Please make sure you have a working internet connection." +
            '\n\nIf the problem still persists, <NAME_EMAIL> and provide this error code. \n\nError code: YPDE004\n\n',
        );
        console.log(
          'Failed to check app availability in your region after several attempts:',
          error,
          'Error code: YPDE004',
        );
        await recordEventFailure(error.message, 'YPDE004');
      }
    }
  };

  const recordEventSuccess = async (data) => {
    const attributes = {
      country: data.components.country,
      state: data.components.state,
      region: data.components.region,
    };

    await recordEvent(
      'Geofencing Event',
      null,
      true,
      '#G1',
      attributes,
      new Date().toISOString(),
    );
  };

  const recordEventFailure = async (message, code) => {
    const coordinates = `${await AsyncStorage.getItem('latitude')}, ${await AsyncStorage.getItem('longitude')}`;
    const state = await AsyncStorage.getItem('state');
    const country = await AsyncStorage.getItem('country');

    const attributes = {
      code: code,
      message: message,
      state: state,
      country: country,
      coordinates: coordinates,
    };

    await recordEvent(
      'Geofencing Event',
      null,
      false,
      '#G1',
      attributes,
      new Date().toISOString(),
    );
  };

  const getLocationPermission = async () => {
    let granted = await checkLocationPermission();

    for (let i = 0; !granted && i < 7; i++) {
      granted = await requestLocationPermission();
      if (
        granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN ||
        Platform.OS === 'ios'
      ) {
        setShowSettingsAlert(true);
        break;
      }
    }
    return granted;
  };

  const initGeofencing = async () => {
    setIsCheckingLocation(true);
    const granted = await getLocationPermission();
    if (granted === 'granted' || granted === true) {
      await getCurrentLocation(0);
      setShowSettingsAlert(false);
    } else {
      setShowSettingsAlert(true);
    }
    setIsCheckingLocation(false);
  };

  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === 'active' && !isCheckingLocation) {
        if (showSettingsAlert || !isInAllowedRegion) {
          console.log('App state change - Re-checking geofencing.');
          await initGeofencing();
        } else {
          console.log('App state change - skipping geofencing check.');
        }
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    initGeofencing();

    return () => {
      subscription.remove();
    };
  }, []);

  const handleAllowLocation = async () => {
    await Linking.openSettings();
  };

  const handleDisallowLocation = () => {
    if (Platform.OS === 'android') {
      BackHandler.exitApp();
    } else {
      Alert.alert(
        'We are sorry',
        'To use location-based features, please enable location permission first.',
        [{ text: 'Got it' }],
      );
    }
  };

  if (showSettingsAlert && !permissionGranted) {
    return (
      <SafeAreaView
        style={[
          styles.geofencingContainer,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <View style={styles.headingContainer}>
          <Heading
            text="Privately Share Your Location"
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <View style={styles.instructionsContainer}>
            <Heading
              text="We use your location to make sure our services follow state laws on medical cannabis.."
              size="md"
              color={theme.colors.text}
            />
          </View>
        </View>
        <View style={styles.imageContainer}>
          <View style={{ padding: 25 }}>
            <Location />
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <CustomButton
            variant="newGreen"
            title="Enable Location Services"
            onPress={handleAllowLocation}
          />
          <CustomButton
            variant="grey"
            title="No Thanks, Close App"
            onPress={handleDisallowLocation}
          />
        </View>
      </SafeAreaView>
    );
  }

  if (!isCheckingLocation && !isInAllowedRegion) {
    return (
      <View style={styles.geofencingContainer}>
        <WarningScreen
          message={
            regionMessage || 'Sorry, this app is not available in your region.'
          }
          fromGeofencing={true}
        />
      </View>
    );
  }

  if (errorCode) {
    return (
      <View style={styles.geofencingContainer}>
        <WarningScreen message={errorCode} fromGeofencing={true} />
      </View>
    );
  }
};
