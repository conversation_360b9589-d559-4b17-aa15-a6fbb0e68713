export const checkLockout = (lastSuggestion, lastCheckinMap) => {
  console.log('Checking time lockout.');

  const now = new Date();

  const thciLastSuggestionTime = lastCheckinMap['thci'];
  const thcoLastSuggestionTime = lastCheckinMap['thco'];
  const cbdiLastSuggestionTime = lastCheckinMap['cbdi'];
  const cbdoLastSuggestionTime = lastCheckinMap['cbdo'];

  const isWithinXHours = (suggestionTime, hours) => {
    const timeDifferenceInHours = (now - suggestionTime) / (1000 * 60 * 60);
    return timeDifferenceInHours <= hours;
  };

  const isLocked =
    isWithinXHours(thciLastSuggestionTime, 1) ||
    isWithinXHours(thcoLastSuggestionTime, 2) ||
    isWithinXHours(cbdiLastSuggestionTime, 1) ||
    isWithinXHours(cbdoLastSuggestionTime, 2);

  if (isLocked) {
    console.log('At least one checkin within the last 1/2 hours.');
    return false;
  }

  if (
    !lastSuggestion ||
    !lastSuggestion.suggestionAt ||
    lastSuggestion?.suggestionType === 'initial'
  ) {
    return true;
  }

  const suggestionTime = new Date(lastSuggestion.suggestionAt);
  const timeDifferenceInHours = (now - suggestionTime) / (1000 * 60 * 60);

  if (timeDifferenceInHours <= 2) {
    const oralSuggestion = ['oral', 'both'].includes(lastSuggestion.dosageType);
    const inhaledSuggestion = ['inhaled', 'both'].includes(
      lastSuggestion.dosageType,
    );

    if (oralSuggestion) {
      console.log('Two-hour time lockout for oral modalities.');
      return false;
    }

    if (timeDifferenceInHours <= 1 && inhaledSuggestion) {
      console.log('One-hour time lockout for inhaled modalities.');
      return false;
    }

    if (timeDifferenceInHours <= 1 && !inhaledSuggestion) {
      console.log('Allow the check-in < 1 hour - no modalities.');
      return true;
    }

    console.log('Allow the check-in < 2 hours - no modalities.');
    return true;
  }

  console.log('Allow the check-in > 2 hours.');
  return true;
};
