import { DefaultTheme, DarkTheme } from '@react-navigation/native';
import {
  ypdWhite,
  ypdBlack,
  ypdTeal,
  ypdTextGrey,
  ypdTextDarkGrey,
  ypdLightGrey,
  ypdRedLight,
  ypdDarkGrey,
  ypdGreen50,
  ypdLink,
  ypdMidBlue,
  ypdOldGreen,
  ypdTextLightGrey,
  ypdGreen,
  whereToBuyBG,
  modalityCardExpandedText,
  ypdBlue,
  ypdMintWhite,
  ypdDefaultGreyGradient,
  ypdHighlightBlue,
} from '../colors';

export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: ypdWhite,
    backgroundRed: ypdRedLight,
    text: ypdBlack,
    textSecondary: ypdTextGrey,
    darkTextGray: ypdTextDarkGrey,
    lightTextGray: ypdLight<PERSON>rey,
    placeholderTextColor: ypdText<PERSON>rey,
    textBlack: ypdBlack,
    cardBackground: ypdWhite,
    border: ypdDarkGrey,
    lightText: ypdTextGrey,
    sliderDotColor: ypdTextGrey,
    linkText: ypdLink,
    homeScreenButton: ypdTextDarkGrey,
    svgFillSelected: ypdBlack,
    svgFillUnselected: ypdBlack,
    svgGear: ypdTextGrey,
    svgColor: ypdLightGrey,
    ypdPrimary: ypdGreen,
    link: ypdLink,
    stepCards: ypdLightGrey,
    reportText: ypdTextGrey,
    whereToByBG: ypdLightGrey,
    modalityCardExpandedText: modalityCardExpandedText,
    remindMeToggleBackground: ypdLightGrey,
    modalityCards: ypdWhite,
    doseBG: ypdMintWhite,
    grayGradient: ypdDefaultGreyGradient,
  },
};

export const darkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    background: ypdTeal,
    backgroundRed: ypdTeal,
    text: ypdWhite,
    textSecondary: ypdTextLightGrey,
    darkTextGray: ypdWhite,
    doseBG: whereToBuyBG,
    lightTextGray: ypdTeal,
    lightText: ypdTextGrey,
    placeholderTextColor: ypdWhite,
    textBlack: ypdBlack,
    cardBackground: ypdTeal,
    border: ypdGreen50,
    sliderDotColor: ypdTextGrey,
    linkText: ypdWhite,
    homeScreenButton: ypdMidBlue,
    svgFillSelected: ypdBlack,
    svgFillUnselected: ypdWhite,
    svgGear: ypdTeal,
    svgColor: ypdOldGreen,
    ypdPrimary: ypdGreen,
    link: ypdGreen,
    stepCards: ypdGreen,
    reportText: ypdGreen,
    whereToByBG: whereToBuyBG,
    modalityCardExpandedText: ypdBlue,
    remindMeToggleBackground: whereToBuyBG,
    modalityCards: whereToBuyBG,
    grayGradient: ypdHighlightBlue,
  },
};
