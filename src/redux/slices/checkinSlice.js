import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  notes: '',
  problemLevel: {},
  overallEffect: 0,
  sideEffects: [],
  customSideEffects: [],
  overAllSideEffect: 0,
};

const checkinSlice = createSlice({
  name: 'checkIn',
  initialState,
  reducers: {
    setNotes: (state, action) => {
      state.notes = action.payload;
    },
    setEffects: (state, action) => {
      state.problemLevel = action.payload;
    },
    setOverallEffect: (state, action) => {
      state.overallEffect = action.payload;
    },
    setOverAllSideEffect: (state, action) => {
      state.overAllSideEffect = action.payload;
    },
    setSideEffects: (state, action) => {
      state.sideEffects = action.payload;
    },
    setCustomSideEffects: (state, action) => {
      state.customSideEffects = action.payload;
    },
    resetCheckinState: () => initialState,
  },
});

export const {
  setNotes,
  setEffects,
  setOverallEffect,
  setSideEffects,
  setOverAllSideEffect,
  setCustomSideEffects,
  resetCheckinState,
} = checkinSlice.actions;
export default checkinSlice.reducer;
