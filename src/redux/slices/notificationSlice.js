import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  appStateCheck: false,
  dailyReminders: false,
  cbdiReminder: true,
  cbdoReminder: true,
  thciReminder: true,
  thcoReminder: true,
  morningReminder: {
    hour: 9,
    min: 0,
  },
  eveningReminder: {
    hour: 19,
    min: 0,
  },
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setDailyReminders(state, action) {
      state.dailyReminders = action.payload;
    },
    setMorningReminder(state, action) {
      state.morningReminder = action.payload;
    },
    setEveningReminder(state, action) {
      state.eveningReminder = action.payload;
    },
    setCBDIReminder(state, action) {
      state.cbdiReminder = action.payload;
    },
    setCBDOReminder(state, action) {
      state.cbdoReminder = action.payload;
    },
    setTHCIReminder(state, action) {
      state.thciReminder = action.payload;
    },
    setTHCOReminder(state, action) {
      state.thcoReminder = action.payload;
    },
    setAppStateCheck(state, action) {
      state.appStateCheck = action.payload;
    },
  },
});

export const {
  setDailyReminders,
  setMorningReminder,
  setEveningReminder,
  setCBDIReminder,
  setCBDOReminder,
  setTHCIReminder,
  setTHCOReminder,
  setAppStateCheck,
} = notificationSlice.actions;

export default notificationSlice.reducer;
