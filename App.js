import { useState, useEffect, useContext, useRef } from 'react';
import { Animated, View } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider, useSelector } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { Amplify } from 'aws-amplify';
import * as Sentry from '@sentry/react-native';

import awsConfig from './src/aws-exports.js';
import { store, persistor } from './src/redux/store';
import { GeoContext } from './src/context/GeoContext';
import { AuthContext, AuthProvider } from './src/context/AuthContext';
import { ThemeProvider } from './src/context/ThemeContext';
import { SubscriptionProvider } from './src/context/SubscriptionContext';
import { loadTheme } from './src/utils/theme/themeLoader';
import { getInitialRoute } from './src/utils/utils';
import SplashLogo from './src/screens/splash/splashLogo';
import App from './src/app';

import styles from './styles.scss';

try {
  Amplify.configure(awsConfig);
} catch (error) {
  console.error('Error configuring Amplify:', error);
}

Sentry.init({
  dsn: process.env.SENTRY_DSN,

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

function MainWrapper({ initialTheme }) {
  const [geoStatus, setGeoStatus] = useState('checking');
  const { isAuthenticated, isLoading } = useContext(AuthContext);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const profileData = useSelector((state) => state.profile);

  const [initialRoute, setInitialRoute] = useState(null);
  const [isSplashVisible, setIsSplashVisible] = useState(true);

  useEffect(() => {
    const initApp = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (!isLoading) {
        const route = getInitialRoute(isAuthenticated, profileData);
        setInitialRoute(route);
        setIsSplashVisible(false);
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }).start();
      }
    };

    initApp();
  }, [isLoading, isAuthenticated, profileData]);

  if (isSplashVisible || !initialRoute || isLoading) {
    return (
      <View style={styles.container}>
        <SplashLogo />
      </View>
    );
  }

  return (
    <PersistGate loading={null} persistor={persistor}>
      <ThemeProvider initialTheme={initialTheme}>
        <GeoContext.Provider value={{ geoStatus, setGeoStatus }}>
          <SubscriptionProvider>
            <NavigationContainer theme={initialTheme.theme}>
              <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
                <App initialRouteName={initialRoute} />
              </Animated.View>
            </NavigationContainer>
          </SubscriptionProvider>
        </GeoContext.Provider>
      </ThemeProvider>
    </PersistGate>
  );
}

export default Sentry.wrap(() => {
  const [ready, setReady] = useState(false);
  const [initialTheme, setInitialTheme] = useState(null);

  useEffect(() => {
    loadTheme().then((theme) => {
      setInitialTheme(theme);
      setReady(true);
    });
  }, []);

  if (!ready) return null;

  return (
    <Provider store={store}>
      <AuthProvider>
        <MainWrapper initialTheme={initialTheme} />
      </AuthProvider>
    </Provider>
  );
});
